---
type: "manual"
---

**Objective:** Integrate a successful solution, sanitize the codebase, and document key learnings.

### Phases

#### Phase 1: Knowledge Distillation & Documentation
- **Identify Learning Artifacts:** Review scripts, comments, and notes
- **Synthesize the Core Lesson** – Structure: Problem, Root Cause, Solution, Takeaway
- **Update `knowledge_base.md`**

```
---
### [YYYY-MM-DD]: [Brief Description of Problem]

* **Problem:** ...  
* **Root Cause:** ...  
* **Solution:** ...  
* **Key Takeaway:** ...
```

- **Update Code-Level Docs** – Add `Note:` or `Gotcha:` to docstrings if applicable

#### Phase 2: Codebase Sanitization & Integration
- **Convert Debug Script to Regression Test**
- **Remove Temporary Artifacts** – Clean `/scripts`
- **Sanitize Production Code** – Remove prints, temp logs, commented blocks
- **Final Linting & Formatting** – Run `ruff`, `black`, or equivalent
- **Propose Final Commit** – Create an atomic commit with clear description