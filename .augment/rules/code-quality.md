---
type: "always_apply"
---

# Code Quality & Architecture Principles

## Core Philosophy
Build systems that are simple to understand, safe to change, and scalable to grow—prioritizing long-term maintainability over short-term convenience.

## The SOLID Foundation

### Single Responsibility Principle
Every module, class, or function should have one reason to change.
- Each component should have a single, well-defined purpose
- If you struggle to name a component without using "and" or "or", it likely has multiple responsibilities
- Separate data transformation from business logic from presentation logic

### Open/Closed Principle
Software entities should be open for extension but closed for modification.
- Design interfaces and abstractions that allow new behavior without changing existing code
- Use composition and dependency injection to enable extensibility
- Prefer configuration over code changes for behavioral variations

### Liskov Substitution Principle
Objects should be replaceable with instances of their subtypes without breaking functionality.
- Ensure derived classes honor the contracts established by base classes
- Design interfaces that can be implemented consistently across different concrete types

### Interface Segregation Principle
Clients should not be forced to depend on interfaces they don't use.
- Create focused, cohesive interfaces rather than large, monolithic ones
- Allow clients to depend only on the methods they actually need

### Dependency Inversion Principle
High-level modules should not depend on low-level modules. Both should depend on abstractions.
- Depend on interfaces or abstractions, not concrete implementations
- Inject dependencies rather than creating them within classes
- Make business logic independent of infrastructure concerns

## Essential Design Patterns

### Separation of Concerns
```
Presentation Layer    → Handles user interface and interaction
Business Logic Layer → Contains core rules and workflows  
Data Access Layer    → Manages persistence and external APIs
Infrastructure Layer → Provides cross-cutting concerns (logging, security)
```

### Composition Over Inheritance
- Use inheritance for "is-a" relationships, composition for "has-a" relationships
- Build complex objects by combining simpler ones
- Create flexible systems through combinable components

### Fail Fast and Fail Clearly
- Detect and report errors as early as possible with maximum clarity
- Validate inputs at system boundaries
- Use strong typing to catch errors at compile time when possible
- Provide contextual error messages that aid in debugging

## Code Quality Standards

### Readability Above Cleverness
Code should be optimized for human understanding, not machine efficiency.
- Choose descriptive names that reveal intent
- Keep functions and methods focused and concise
- Use consistent formatting and style conventions
- Comment the "why" not the "what"

### Consistency in All Things
Maintain consistent patterns, naming, and structure throughout the codebase.
- Establish and document coding conventions for the team
- Use automated formatting tools to eliminate style debates
- Follow existing patterns within the codebase

### Modularity and Loose Coupling
Design components that can be understood and modified independently.
- Minimize dependencies between modules
- Use well-defined interfaces for component communication
- Avoid shared mutable state across module boundaries
- Design for testability through dependency injection
