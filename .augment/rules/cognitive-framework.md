---
type: "always_apply"
---

# Cognitive Framework & Decision Making

## Core Philosophy
Develop systematic thinking patterns that lead to better architectural decisions, faster problem resolution, and more maintainable solutions.

## Mental Models for Software Development

### 1. Systems Thinking Model
View software as interconnected systems where changes ripple through multiple layers and components.
- Map data flow and dependencies before making changes
- Consider second and third-order effects of architectural decisions
- Identify feedback loops and potential cascade failures
- Think in terms of interfaces and contracts between components

**Key Questions:**
- What upstream systems provide data or services to this component?
- What downstream systems depend on this component's output?
- How will this change affect system reliability, performance, and maintainability?

### 2. Abstraction and Modeling
Build mental models that capture essential complexity while hiding irrelevant details.
- Identify the core concepts and relationships in the problem domain
- Create abstractions that match business concepts, not technical implementation
- Use appropriate levels of abstraction for different audiences and purposes
- Design interfaces that expose intent, not implementation

### 3. Trade-off Analysis Framework
Every decision involves trade-offs. Make them explicit and choose consciously.

**Common Trade-offs:**
- Performance vs. Maintainability: Optimized code may be harder to understand
- Flexibility vs. Simplicity: More flexible systems often have more complexity
- Speed of Development vs. Quality: Rushing features may create technical debt
- Build vs. Buy: Custom solutions vs. third-party tools each have costs and benefits

**Decision Framework:**
1. Identify the key stakeholders and their priorities
2. List the primary options and their trade-offs
3. Quantify impacts where possible (performance, cost, time)
4. Consider both short-term and long-term implications
5. Make the decision explicit and document the rationale

## Decision-Making Frameworks

### Risk-Based Decision Making
Evaluate decisions based on their risk profile and potential impact.

**Risk Assessment Matrix:**
- High Impact + High Probability = Critical Risk
- High Impact + Low Probability = Medium Risk
- Low Impact + High Probability = Medium Risk
- Low Impact + Low Probability = Low Risk

**Risk Mitigation Strategies:**
- Accept: For low-risk scenarios where mitigation cost exceeds benefit
- Avoid: Change approach to eliminate the risk entirely
- Mitigate: Reduce probability or impact through design choices
- Monitor: Track risk indicators and have contingency plans ready

### The Five Whys Technique
Drill down to root causes by asking "Why?" repeatedly.

**Example:**
1. Problem: The application is running slowly
2. Why? Database queries are taking too long
3. Why? We're not using indexes effectively
4. Why? The query patterns changed with new features
5. Why? We didn't update our indexing strategy when adding features
6. Why? We don't have a process for reviewing database performance impact

## Cognitive Biases and Mental Traps

### Common Biases
- **Confirmation Bias**: Looking for evidence that supports your initial hypothesis
- **Sunk Cost Fallacy**: Continuing with a poor approach because of time already invested
- **Availability Heuristic**: Overweighting recent or memorable experiences

### Mitigation Techniques
- Actively seek disconfirming evidence
- Regularly reassess decisions based on current value, not past investment
- Use data and metrics rather than anecdotal evidence for decision-making
