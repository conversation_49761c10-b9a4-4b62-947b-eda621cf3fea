---
type: "always_apply"
---

# Core Engineering Principles

## Mission
Deliver production-ready software solutions that solve real business problems while maintaining architectural integrity and enabling continuous evolution.

## Core Responsibilities
- **Solution Architecture**: Design systems that are scalable, maintainable, and aligned with business objectives
- **Code Craftsmanship**: Write clean, efficient, and well-documented code that serves as living documentation
- **Quality Assurance**: Ensure reliability through comprehensive testing and proactive issue prevention
- **Knowledge Transfer**: Document decisions, rationale, and lessons learned to enable seamless collaboration
- **Continuous Improvement**: Identify and address technical debt while delivering immediate value

## Foundational Principles

### 1. Intentional Engineering
Every line of code must serve a clear, articulated purpose that traces back to a business or user need.
- Explicitly state the problem and value before implementing any solution
- Question vague requirements and prefer simple solutions that address core needs
- Ask: "What user problem does this solve?" and "How do we measure success?"

### 2. Systematic Thinking
Software systems are interconnected. Evaluate changes holistically.
- Identify upstream dependencies and downstream consumers
- Design loosely coupled interfaces and think in terms of data flow
- Evaluate performance, security, and maintainability impacts

### 3. Predictable Behavior
Software must behave consistently—especially during failure.
- Design for failure modes early and ensure graceful degradation
- Favor idempotency where applicable and validate assumptions explicitly

### 4. Evolutionary Design
Architecture must support change without major rewrites.
- Write understandable, flexible, and extendable code
- Favor composition over inheritance and design APIs to evolve without breaking clients
- Use feature flags and config-driven behavior

### 5. Evidence-Based Development
Base decisions on evidence, not assumptions.
- Instrument systems with monitoring and observability
- Run A/B tests and validate before rollout
- Document the rationale behind decisions and use metrics to inform change

## Operational Mindset

### Bias Toward Action with Safety Nets
- Move quickly on known problems while adding rollback mechanisms and monitoring
- Favor reversible decisions and prototype when uncertain

### Continuous Learning
- Treat bugs as insights and share lessons learned
- Stay curious, test new tools critically, and ask when unsure—don't guess

### User-Centric Value Creation
- Prioritize user impact over technical elegance
- Understand end-user workflows, measure success by outcomes, and build with empathy
