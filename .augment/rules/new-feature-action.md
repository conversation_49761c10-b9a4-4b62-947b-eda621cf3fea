---
type: "manual"
---

**Objective:** Plan and implement a new feature systematically, starting with a minimum working skeleton, aligning with project architecture, and growing it incrementally while drawing on external and internal knowledge sources.

### Phases

#### Phase 1: Feature Planning
- **Parse `dev_notes.md`** – Identify recent objectives, priorities, and blockers
- **Parse `project_plan.md` or `architecture.md`** – Check for architecture, style guides, naming conventions
- **If no planning/architecture documents exist:**
  - Notify the user with: "⚠️ Project lacks sufficient planning documentation. Feature cannot be reliably scoped. Recommend creating `project_plan.md` or equivalent."
- **Create Planning Section in `dev_notes.md`**

```
## Feature: [Feature Name]

- **Problem Statement:** ...  
- **Proposed Solution:** ...  
- **Dependencies:** ...  
- **Edge Cases:** ...  
- **Testing Strategy:** ...
```

#### Phase 2: Research & Design Validation
- **Consult the Research Protocol**
  - Look up tutorials, docs, patterns that apply to this feature
  - Summarize findings inline and link references
- **Validate Architecture Fit**
  - Confirm the solution conforms to the existing project architecture/style
  - Flag any mismatches or unclear integration points

#### Phase 3: Bootstrapping the Feature
- **Scaffold the Minimum Viable Code**
  - Create empty class/module/function with docstrings
  - Add TODOs for each expected responsibility
- **Write initial test** for simplest valid behavior (TDD first step)
- **Commit milestone:** "chore: scaffold feature [name] with TDD entry point"

#### Phase 4: Incremental Development
- **Implement smallest working logic first**
- Expand with real inputs and control paths
- After each success milestone, update `dev_notes.md`

#### Phase 5: Documentation & Integration
- **Update `README.md` or feature-specific docs**
- **Add any relevant learnings to `knowledge_base.md`**
- **Mark feature as "Ready for Review" in `dev_notes.md`**
