---
type: "always_apply"
---

# Problem-Solving & Debugging Methodology

## Core Philosophy
Transform every issue into an opportunity for system understanding and improvement through systematic investigation and evidence-based resolution.

## The Scientific Debugging Method

### 1. Evidence-First Principle
Collect observable facts before forming theories about the problem.
- Reproduce the issue consistently before attempting fixes
- Gather logs, error messages, and system state information
- Document exact conditions under which the problem occurs
- Identify what changed since the system last worked correctly

### 2. Hypothesis-Driven Investigation
Form testable theories about the cause and validate them systematically.
- Generate multiple competing hypotheses for the issue
- Design specific tests to prove or disprove each hypothesis
- Test one variable at a time to isolate the actual cause
- Use binary search techniques to narrow down problem domains

### 3. Minimal Viable Fix
Implement the smallest change that definitively resolves the issue.
- Make targeted fixes rather than broad changes
- Avoid "while we're here" improvements during bug fixes
- Verify the fix addresses the root cause, not just symptoms

## Systematic Resolution Process

### Step 1: Problem Definition
- What is the expected vs. actual behavior?
- When did this problem first appear?
- What is the impact and severity?
- Who is affected and under what conditions?

### Step 2: Evidence Gathering
- Examine logs around the time of failure
- Check system resource utilization (CPU, memory, disk, network)
- Review recent deployments, configuration changes, or data updates
- Compare behavior across different environments

### Step 3: Hypothesis Testing
- Brainstorm potential causes based on evidence
- Test each hypothesis independently using debugging tools
- Implement temporary logging or monitoring to gather more data
- Document test results and observations

### Step 4: Resolution and Verification
- Apply the minimal change necessary to resolve the issue
- Test the fix in a controlled environment first
- Monitor system behavior after applying the fix
- Verify that related functionality remains unaffected

## Advanced Techniques

### Divide and Conquer
- Binary search through code commits to find when regression was introduced
- Disable non-essential features to identify if they contribute to the issue
- Test individual components in isolation

### Rubber Duck Debugging
- Walk through the code logic line by line
- Explain the data flow and transformations
- Question each step: "Why should this work?" and "What could go wrong here?"

### Collaborative Debugging
- Share context and evidence gathered so far
- Explain what you've already tried and ruled out
- Ask specific questions rather than general "it's broken" statements

## Documentation and Learning
- Document problem description, investigation steps, and root cause analysis
- Create runbooks for frequently occurring problems
- Update monitoring and alerting based on new failure modes discovered
- Use every debugging session as an opportunity to improve the system
