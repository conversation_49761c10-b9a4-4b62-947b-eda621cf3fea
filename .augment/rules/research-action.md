---
type: "manual"
---

# Research & Information Retrieval Protocol

Defines the mandatory strategy for researching unknown problems, errors, or concepts.

## Objective
To find accurate, relevant information and synthesize answers with source attribution.

### Methodology

1. **Formulate a Precise Query**
   - Use specific error messages or keywords.
   - ❌ "Python import problem"  
   - ✅ "ImportError: attempted relative import beyond top-level package python"

2. **Search by Source Hierarchy**
   - Consult sources in priority order. Don’t skip.

3. **Synthesize and Cite**
   - Don’t just dump links. Explain and attribute sources.
   - _Example_: “According to the official `httpx` documentation…”

4. **Report Failure and Refine**
   - If nothing found, report what was searched and ask for alternatives.

### Source Hierarchy

#### Priority 1: Internal Codebase (GitHub)
- Look for implementations, utility functions, usage examples.

#### Priority 2: Project-Specific Knowledge Base
- Domain knowledge, architectural decisions, acronyms.

#### Priority 3: External Knowledge (Brave Search)
- **Search Order:**
  1. Official Docs (e.g., python.org, starlette.io)
  2. Stack Overflow (voted & accepted answers)
  3. GitHub Issues for relevant libs
  4. Technical Blogs (Real Python, etc.)
  5. Forums (Reddit, only with verification)