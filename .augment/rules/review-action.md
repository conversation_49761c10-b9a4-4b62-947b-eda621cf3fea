---
type: "manual"
---

**Objective:** Efficiently and safely resume work by establishing a shared understanding of the project’s current state and verifying its stability.

### Phases

#### Phase 1: Context Synthesis
- **Parse `README.md`** – Identify the project’s overall purpose and core features. *(Required)*
- **Parse `dev_notes.md`** – Identify most recent work, next steps, and blockers. *(Required)*
- **Parse `project_plan.md`** – Align tasks with roadmap. *(Optional)*
- **Report Context Summary**

```
**Project Context Review:**

* **Project Goal:** [Summary from README.md]  
* **Current Status:** [Summary from dev_notes.md]  
* **Next Objective:** [Summary of next steps from dev_notes.md and project_plan.md]
```

#### Phase 2: Stability Verification
- **Announce Verification Plan**
- **Execute Test Verification**
  - **Conceptual Review (Default):** Read test files, check for skips or incomplete tests.
  - **Execution (Preferred):** Run test suite and capture results.
- **Report Stability Summary**

```
**Code Stability Review:**

* **Test Suite Status:** OK | ATTENTION_NEEDED | FAILING  
* **Summary:** [e.g., Reviewed 25 tests across 4 files.]  
* **Action Items:** [List of failing or incomplete tests.]
```

#### Phase 3: Readiness Confirmation
- **Declare System Status**
  - ✅ **System Ready** – Context is synchronized and codebase is stable.
  - ⚠️ **Attention Required** – Issues detected. Include a brief summary.