---
type: "always_apply"
---

# Testing Philosophy

## Core Philosophy
Create a comprehensive suite of executable specifications that validate all critical system behaviors, providing confidence in changes and serving as living documentation.

## The Four Pillars of Testing Excellence

### 1. The Golden Rule of Test Integrity
Application code changes to make tests pass. Tests never change to accommodate faulty application code.
- A failing test indicates a problem in the application logic, not the test specification
- When tests fail after code changes, first assume the application code is incorrect
- Only modify tests when requirements have genuinely changed

### 2. Tests as Executable Documentation
Every test must clearly communicate a specific behavioral requirement.
- Test names should read like specifications: `should_calculate_correct_discount_when_coupon_is_valid`
- Use the Given-When-Then pattern to structure test logic clearly
- Focus on testing behaviors and outcomes, not implementation details

### 3. Comprehensive Behavioral Coverage
Achieve 100% coverage of critical user journeys and business logic, not necessarily 100% code coverage.
- Every user story or business requirement must have corresponding test coverage
- Prioritize testing critical paths and edge cases over comprehensive code coverage
- Use risk-based testing to focus effort on high-impact, high-probability failure scenarios

### 4. Strategic Test Distribution
Apply the testing pyramid to optimize for both speed and confidence.

```
    /\     E2E Tests (15%)
   /  \    Slow, comprehensive, high confidence
  /____\   Test complete user workflows
 /      \  
/        \  Integration Tests (35%)
\        /  Medium speed, moderate scope
 \______/   Test component interactions with real dependencies
/        \  
\        /  Unit Tests (50%)
 \______/   Fast, focused, high volume
            Test individual components
```

## Anti-Mocking Guidelines: The Dangerous Mock Trap

### **NEVER Mock These Components**
Business logic classes must be tested directly, never mocked:

- **Domain Services**: `BookingBusinessRules`, `TimeService`, validation logic
- **Business Rules**: Booking limits, state transitions, availability calculations  
- **Core Logic**: Timezone handling, date calculations, user permissions
- **Repository Business Methods**: Server-side validation, data transformation

### **Acceptable Mocking Targets**
Mock only external dependencies and infrastructure:

- **External APIs**: Supabase client, payment gateways, email services
- **System Dependencies**: File system, network calls, device sensors
- **Third-party Libraries**: Analytics, crash reporting, push notifications
- **Infrastructure**: Database connections (only when testing business logic in isolation)

### **The Mock Validation Test**
Before mocking any class, ask: "If this mock returns the wrong data, would my test still pass?"
- If YES → Dangerous mock that hides business logic bugs
- If NO → Acceptable mock of external dependency

```dart
// ❌ DANGEROUS: Mocking business logic
when(mockBookingRules.isUpcomingBooking(booking, now)).thenReturn(true);
// Test passes even if real business logic is broken

// ✅ SAFE: Testing real business logic
final result = BookingBusinessRules.isUpcomingBooking(booking, now);
expect(result, isTrue);
```

## Greenfield Database Testing Strategy

### **Leverage Real Database Integration**
As a greenfield project with dedicated test infrastructure, prioritize real database testing:

### **Database Testing Hierarchy**
1. **Unit Tests**: Mock database for pure business logic
2. **Integration Tests**: Use real test database for data flows
3. **E2E Tests**: Full database integration with cleanup

### **Real Database Integration Guidelines**

```dart
// Test database setup
class DatabaseTestHelper {
  static late SupabaseClient testClient;
  
  static Future<void> setupTestDatabase() async {
    testClient = SupabaseClient(
      'test-project-url',
      'test-anon-key',
    );
  }
  
  static Future<void> cleanupTestData() async {
    // Clean test data between tests
    await testClient.from('bookings').delete().neq('id', 'never-matches');
    await testClient.from('time_slots').delete().neq('id', 'never-matches');
  }
}
```

### **When to Use Real Database**
- **Provider Integration Tests**: Test real data flow through Riverpod providers
- **Business Rule Validation**: Verify database constraints and triggers
- **State Transition Tests**: Test booking status changes with real persistence
- **Timezone Edge Cases**: Validate UTC handling with actual database queries

### **Database Isolation Strategies**
- **Test-specific data**: Create unique test data per test case
- **Cleanup hooks**: Automatic cleanup in `tearDown()` methods
- **Parallel execution**: Use unique identifiers to prevent test interference
- **Schema versioning**: Test against same database schema as production

## Business Logic Testing Principles

### **Domain Service Testing**
Test core business services without mocking their internal logic:

```dart
group('BookingBusinessRules', () {
  test('should correctly categorize bookings by time', () {
    final now = DateTime.parse('2025-01-10T14:00:00Z');
    final upcomingBooking = createTestBooking(
      slotStartTime: DateTime.parse('2025-01-10T15:00:00Z'),
    );
    
    // Test REAL business logic
    final category = BookingBusinessRules.categorizeBooking(upcomingBooking, now);
    
    expect(category, BookingCategory.upcoming);
  });
});
```

### **TimeService Testing**
Validate timezone handling with real implementations:

```dart
group('TimeService', () {
  test('should handle timezone boundaries correctly', () {
    final timeService = TimeService();
    
    // Test real timezone conversion
    final utcTime = timeService.toUtc(localDateTime);
    final backToLocal = timeService.toLocal(utcTime);
    
    expect(backToLocal.day, equals(localDateTime.day));
  });
});
```

### **Repository Testing Strategy**
Test business logic in repositories with real database calls:

```dart
group('ServerSideBookingRepository', () {
  late ServerSideBookingRepository repository;
  
  setUp(() async {
    await DatabaseTestHelper.setupTestDatabase();
    repository = ServerSideBookingRepository(DatabaseTestHelper.testClient);
  });
  
  tearDown(() async {
    await DatabaseTestHelper.cleanupTestData();
  });
  
  test('should enforce booking limits with real database', () async {
    // Create 4 test bookings
    await createTestBookings(count: 4, userId: 'test-user');
    
    // Test real limit enforcement
    final canBook = await repository.canUserBookMore('test-user');
    
    expect(canBook, isFalse);
  });
});
```

## Enhanced Integration Test Strategy

### **Greenfield Advantage: More Integration Tests**
With dedicated test infrastructure, we can afford comprehensive integration testing:

```
    /\     E2E Tests (15%)
   /  \    Complete user workflows
  /____\   Real UI + Real Backend
 /      \  
/        \  Integration Tests (35%) ← INCREASED
\        /  Real providers + Real database
 \______/   Component interaction testing
/        \  
\        /  Unit Tests (50%) ← FOCUSED
 \______/   Pure business logic only
            No infrastructure mocking
```

### **Integration Test Categories**

#### **Provider Integration Tests**
Test Riverpod providers with real implementations:

```dart
testWidgets('availability provider should load real slots', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        // Only override external dependencies
        supabaseClientProvider.overrideWithValue(DatabaseTestHelper.testClient),
      ],
      child: Consumer(
        builder: (context, ref, child) {
          final availability = ref.watch(availabilityProvider('test-pitch'));
          return availability.when(
            data: (slots) => Text('${slots.length} slots'),
            loading: () => CircularProgressIndicator(),
            error: (error, stack) => Text('Error: $error'),
          );
        },
      ),
    ),
  );
  
  await tester.pumpAndSettle();
  
  // Verify real data loading
  expect(find.textContaining('slots'), findsOneWidget);
});
```

#### **Business Flow Integration Tests**
Test complete business workflows with real dependencies:

```dart
group('Booking Flow Integration', () {
  test('should complete booking with real business logic', () async {
    // Setup real test data
    await createTestPitch(id: 'test-pitch', maxBookings: 4);
    await createTestSlots(pitchId: 'test-pitch', date: tomorrow);
    
    final bookingService = BookingService(DatabaseTestHelper.testClient);
    
    // Test real booking creation
    final result = await bookingService.createBooking(
      pitchId: 'test-pitch',
      slotStartTime: tomorrowAt9AM,
      userId: 'test-user',
    );
    
    expect(result.isSuccess, isTrue);
    
    // Verify real database state
    final bookings = await DatabaseTestHelper.testClient
        .from('bookings')
        .select()
        .eq('user_id', 'test-user');
    
    expect(bookings.length, equals(1));
  });
});
```

## Provider Testing Guidelines

### **Real Implementation Testing**
Test Riverpod providers with actual business logic, not overrides:

```dart
// ❌ AVOID: Over-using provider overrides
testWidgets('booking count display', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        activeBookingCountProvider.overrideWith((ref) => 3), // Mocks business logic
      ],
      child: BookingCountWidget(),
    ),
  );
});

// ✅ PREFER: Test real provider with real data
testWidgets('booking count display with real data', (tester) async {
  // Setup real test data
  await createTestBookings(count: 3, userId: 'test-user');
  
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        // Only override external dependencies
        supabaseClientProvider.overrideWithValue(DatabaseTestHelper.testClient),
        currentUserProvider.overrideWith((ref) => User(id: 'test-user')),
      ],
      child: BookingCountWidget(),
    ),
  );
  
  await tester.pumpAndSettle();
  
  // Verify real business logic result
  expect(find.text('3 bookings'), findsOneWidget);
});
```

### **Provider Override Guidelines**
Only override these provider types:
- **Authentication**: Current user, session state
- **External Services**: Supabase client, API clients
- **System Dependencies**: Device info, connectivity

Never override:
- **Business Logic Providers**: Booking rules, availability calculations
- **Data Transformation Providers**: State categorization, filtering logic
- **Validation Providers**: Form validation, business rule checking

## Test-Driven Development Workflow

### The Red-Green-Refactor Cycle

**Step 1: Define the Behavior (RED)**
- Write a clear behavioral specification in Given-When-Then format
- Implement this as a failing test that defines the expected outcome
- Confirm the test fails for the right reason

**Step 2: Implement Minimally (GREEN)**
- Write the simplest possible code to make the test pass
- Resist the urge to add features not covered by the current test
- Focus solely on satisfying the test requirements

**Step 3: Refactor Confidently (REFACTOR)**
- With tests passing, improve code structure, readability, and performance
- Refactor both application code and test code for maintainability
- Ensure tests continue to pass throughout refactoring process

## Testing Strategy by System Layer

### Unit Testing (The Foundation)
**Scope**: Individual functions, methods, or classes in complete isolation
**Speed**: Milliseconds per test
**Dependencies**: All external dependencies mocked or stubbed

### Integration Testing (The Contracts)
**Scope**: How multiple internal components work together
**Speed**: Seconds per test
**Dependencies**: Mock only external systems, use real internal dependencies

### End-to-End Testing (The User Journey)
**Scope**: Complete user workflows through the entire system
**Speed**: Minutes per test
**Dependencies**: Minimal mocking, use production-like environment

## Test Organization & Structure

### Test Structure Template
```
test_should_[expected_behavior]_when_[conditions]:
    // Given: Set up the initial state
    // When: Execute the behavior being tested  
    // Then: Verify the expected outcome
```

### Quality Gates
- All tests must pass before code can be committed
- New functionality requires corresponding test coverage
- No commented-out or skipped tests without documented justification
- Business logic must be tested without mocking core domain services
