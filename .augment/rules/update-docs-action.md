---
type: "manual"
---


## Command: "Update the Docs"

**Triggers:**
- Let’s update the docs
- Please bring the documentation up to date
- Regenerate planning docs from current state

**Objective:** Ensure all documentation is synchronized with the latest code, tests, and decisions. Eliminate stale information.

### Phases

#### Phase 1: State Snapshot
- Parse: `README.md`, `dev_notes.md`, `project_plan.md`, `changelog.md`, `knowledge_base.md`, `known_issues.md`, `ADRs` test files, and any implemented feature docs.
- Identify any inconsistency, missing updates, or unresolved "TODO" markers.

#### Phase 2: Structured Regeneration
- For each relevant file:
  - Propose inline updates to reflect current reality.
  - Add/update missing test documentation, changelog entries, known limitations.
  - Ensure roadmap, completed tasks, and in-progress states are synchronized.

#### Phase 3: Draft Commit Proposal
- Summarize the changes by file
- Prepare a Git commit:
  - Message format: `docs: sync documentation with current project state`
  - Include details of updated sections
  - Flag undocumented features or functionality
  - Update architecture diagrams (if any)
  - Trigger a backup to long-term knowledge store (e.g., `knowledge_base.md`)
  - Notify user if key files (`README.md`, `project_PRD.md`) are missing or misaligned with real code

---

Operational Protocol: Let's Update the Docs
Objective: To perform a comprehensive synchronization of all project documentation with the current state of the codebase, ensuring accuracy, consistency, and a clear historical record for all stakeholders.

Triggers: "Let's update the docs," "Time to sync the documentation," "Update the changelog."

Phase 1: Impact Analysis & Information Gathering
This phase determines what needs to be updated by gathering evidence of all recent changes and auditing the existing documentation for staleness.

Synthesize Recent Work: Review the following sources to build a complete picture of what has been accomplished since the last documentation update:

The git log and recent commit history to identify merged features and fixes.

The dev_notes.md for in-progress commentary and rationale.

The knowledge_base.md for key lessons learned from resolved issues.

The test suite (/tests) to confirm the behavior of newly implemented functionality.

Perform Documentation Health Check: Based on the synthesized changes, audit all user-facing documentation for accuracy. You must check the following:

Project Plan (project_plan.md): Have any tasks or milestones been completed?

Changelog (CHANGELOG.md): Is it missing any recent user-facing features, bug fixes, or breaking changes?

API Documentation (e.g., OpenAPI/Swagger Spec, Markdown files): Have any endpoints, request payloads, or response schemas been added, changed, or removed?

Architectural Diagrams (e.g., Mermaid.js, images): Are any diagrams depicting system structure now out of date?

Main Readme (README.md):

Is the high-level project description still accurate?

Are the installation and setup instructions correct, especially after any dependency changes?

Are all code examples and usage snippets still valid and functional?

Phase 2: Content Generation & Drafting
Based on the impact analysis, generate the new content for all affected documents.

Draft CHANGELOG.md Entries: For each significant user-facing change, create a new entry following best practices (e.g., Keep a Changelog).

Example Changelog Entry:

## [1.2.0] - 2025-06-12
### Added
- **User Profile Avatars:** Users can now upload and manage profile pictures via the `/api/users/me/avatar` endpoint.

### Fixed
- Resolved a bug where login would fail for usernames containing special characters.

Update README.md and Guides: Draft the necessary changes for installation instructions, code examples, and general descriptions.

Update project_plan.md: Mark completed tasks with a [x] and update the status of ongoing epics or milestones.

Propose Other Doc Changes: Prepare updates for API docs, architectural diagrams, or any other files identified in Phase 1.

Phase 3: User Review and Confirmation
Present all proposed documentation changes to the user for approval before modifying any files. This is a critical validation step.

Present a Summary of Changes: Provide a clear, high-level overview.

"I have analyzed the recent changes and drafted updates for the following files: CHANGELOG.md, README.md, and project_plan.md."

Show Proposed Diffs: For each file, present the proposed changes in a clear "diff" format for easy review.

Proposed changes for CHANGELOG.md:

+ ## [1.2.0] - 2025-06-12
+ ### Added
+ - **User Profile Avatars:** Users can now upload and manage profile pictures via the `/api/users/me/avatar` endpoint.

Await Explicit Approval: Halt the process until the user gives explicit approval to proceed. Ask a direct question, such as: "Do you approve these changes?"

Phase 4: Finalization and Commit
Once the user approves the changes, apply them and create a clean, well-documented commit.

Apply Changes: Modify the project files with the approved content.

Draft Commit Message: Following the communication_protocol, draft an appropriate commit message. Documentation updates are typically a docs type in Conventional Commits.

Example Commit Message:

docs: update changelog and readme for version 1.2.0

- Updates CHANGELOG.md with details on the new user avatar feature and a recent bug fix.
- Updates README.md to include an example for the new avatar upload API endpoint.
- Marks the 'User Profile V1' epic as complete in the project plan.

Execute Commit: Once the commit message is approved, perform the commit. The protocol is now complete.
