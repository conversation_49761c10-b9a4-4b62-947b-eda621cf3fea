# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Test Quality Improvement Initiative**: Comprehensive test refactoring plan to eliminate over-mocking anti-patterns
  - Created detailed roadmap in `docs/test_refactoring_plan.md` for eliminating dangerous business logic mocks
  - Identified 23+ tests that inappropriately mock `TimeService`, `BookingBusinessRules`, and `ServerSideBookingRepository`
  - Established patterns for real business logic testing with controlled dependencies
  - Planned integration test infrastructure leveraging greenfield database advantages

### Technical Debt
- **Over-Mocking Anti-Patterns**: Documented systematic issues where tests mock business logic instead of external dependencies
  - Tests currently mock `TimeService`, hiding time-based business logic bugs
  - Tests mock `ServerSideBookingRepository`, bypassing repository business logic validation
  - Provider tests use excessive overrides, avoiding real business logic execution
  - Investigation tests mock core business logic, providing negative testing value

### Planned
- **Phase 1**: Delete harmful tests that mock business logic (Week 1)
- **Phase 2**: Refactor critical business logic tests to use real implementations (Week 2)  
- **Phase 3**: Enhance integration test coverage with real database testing (Week 3)
- **Phase 4**: Improve provider testing patterns to minimize overrides (Week 4)

## [1.0.0] - 2025-01-XX

### Added
- Initial project setup with Flutter and Supabase integration
- Booking system with real-time availability
- User authentication and profile management
- Pitch management and time slot booking
- Optimistic UI updates for booking operations

### Infrastructure
- Riverpod state management implementation
- Supabase backend integration
- Real-time data synchronization
- Comprehensive test suite (with identified quality issues)

---

*Note: This changelog will be updated as the test refactoring initiative progresses through its implementation phases.*
