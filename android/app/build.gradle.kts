plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.mw.skillz"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.mw.skillz"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
            
            // Disable minification to prevent Supabase issues
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }
}

// 👇 Post-build task to create branded APK copy
gradle.projectsEvaluated {
    tasks.named("assembleRelease").configure {
        doLast {
            val versionName = android.defaultConfig.versionName ?: "1.0.0"
            val originalApk = file("$buildDir/outputs/flutter-apk/app-release.apk")
            val brandedApk = file("$buildDir/outputs/flutter-apk/skillz-v$versionName.apk")
            
            if (originalApk.exists()) {
                originalApk.copyTo(brandedApk, overwrite = true)
                println("✓ Created branded APK: skillz-v$versionName.apk")
            }
        }
    }
}

flutter {
    source = "../.."
}
