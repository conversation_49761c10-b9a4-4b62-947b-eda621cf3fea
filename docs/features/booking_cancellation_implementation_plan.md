# Booking Cancellation Feature Implementation Plan

**Date**: January 2025  
**Feature**: Booking Cancellation with Business Rules  
**Approach**: Test-Driven Development (TDD)  
**Estimated Total Time**: 6-8 hours (20-minute tasks)

## Executive Summary

Implement booking cancellation feature that allows:
1. **Active Booking Cancellation**: Cancel confirmed bookings within the allowed cancellation window (backend enforced)
2. **Past Booking Deletion**: Hide/delete past bookings from user's local view (frontend only)
3. **Business Rules**: Enforce cancellation window (configurable per pitch in `pitch_settings.cancellation_window_hours`)
4. **Error Handling**: Clear feedback for expired cancellation window and other edge cases

## Context Analysis

### Current State
- **UI Placeholder**: Cancel button exists in `my_bookings_screen.dart` line 176 with TODO comment
- **Backend Ready**: Database schema has `cancellation_window_hours` in `pitch_settings` table
- **Infrastructure**: Server-side booking state management (ADR-008) is complete
- **Architecture**: Optimistic booking service patterns established for state management

### Business Rules Identified
1. **Cancellation Window**: Stored per pitch in `pitch_settings.cancellation_window_hours`
2. **Time Validation**: Only allow cancellation if `slotStartTime - now > cancellationWindow`
3. **Status Restrictions**: Only cancel bookings with status `confirmed` or `pendingPayment`
4. **Past Bookings**: Allow deletion from local view (no backend validation needed)

## Task Breakdown (20-minute increments)

### Phase 1: Test Planning & Domain Layer (60 minutes)

#### Task 1.1: Create Cancellation Business Rules Tests (20 min)
- Create failing tests for `BookingCancellationRules` domain class
- Test cancellation window validation
- Test status restrictions
- Test edge cases (exactly at boundary, past bookings)

#### Task 1.2: Implement Cancellation Business Rules (20 min)
- Create `lib/features/booking/domain/booking_cancellation_rules.dart`
- Implement `canCancelBooking()` method
- Implement `getCancellationDeadline()` method
- Make tests pass

#### Task 1.3: Create Cancellation Exceptions & Models (20 min)
- Add `CancellationExpiredException` to booking exceptions
- Add `CancellationNotAllowedException` 
- Create cancellation request/response models
- Update exports

### Phase 2: Backend Implementation (60 minutes)

#### Task 2.1: Backend Repository Tests (20 min)
- Create failing tests for `cancelBooking()` method in `BookingRepository`
- Test successful cancellation
- Test expired window error
- Test invalid status error

#### Task 2.2: Implement Backend Cancellation Logic (20 min)
- Add `cancelBooking()` method to `BookingRepository`
- Implement database update with status change to `cancelledByUser`
- Add business rule validation
- Make repository tests pass

#### Task 2.3: Edge Function Implementation (20 min)
- Create or extend edge function for atomic cancellation
- Add validation for cancellation window
- Test integration with repository
- Handle race conditions

### Phase 3: Application Layer (40 minutes)

#### Task 3.1: Cancellation Service Tests (20 min)
- Create failing tests for cancellation service
- Test optimistic cancellation flow
- Test error handling and rollback
- Test provider invalidation

#### Task 3.2: Implement Cancellation Service (20 min)
- Create `BookingCancellationService` with Riverpod
- Implement optimistic cancellation with rollback
- Handle errors and user feedback
- Integrate with existing booking state management

### Phase 4: UI Implementation (60 minutes)

#### Task 4.1: UI Component Tests (20 min)
- Create widget tests for cancellation confirmation dialog
- Test cancellation button states (enabled/disabled)
- Test error message display
- Test success feedback

#### Task 4.2: Cancellation Confirmation Dialog (20 min)
- Create `BookingCancellationDialog` widget
- Show cancellation deadline and warning
- Implement confirmation and cancel actions
- Add loading states

#### Task 4.3: Integrate with My Bookings Screen (20 min)
- Connect cancel button to cancellation service
- Implement button state logic (enabled/disabled based on rules)
- Add "Delete from view" option for past bookings
- Test complete user flow

### Phase 5: Integration & Polish (40 minutes)

#### Task 5.1: Integration Testing (20 min)
- Test complete cancellation flow
- Test real-time updates after cancellation
- Test provider invalidation
- Fix any integration issues

#### Task 5.2: Error Handling & UX Polish (20 min)
- Improve error messages and user feedback
- Add loading states and animations
- Test edge cases and error scenarios
- Update documentation

## Implementation Details

### Key Files to Create/Modify

**New Files:**
- `lib/features/booking/domain/booking_cancellation_rules.dart`
- `lib/features/booking/application/booking_cancellation_service.dart`
- `lib/features/booking/presentation/widgets/booking_cancellation_dialog.dart`
- `test/features/booking/domain/booking_cancellation_rules_test.dart`
- `test/features/booking/application/booking_cancellation_service_test.dart`
- `test/features/booking/presentation/widgets/booking_cancellation_dialog_test.dart`

**Modified Files:**
- `lib/features/booking/data/booking_repository.dart` (add cancelBooking method)
- `lib/features/booking/presentation/my_bookings_screen.dart` (connect cancel button)
- `lib/features/booking/domain/booking_exceptions.dart` (add cancellation exceptions)
- `lib/features/booking/data/server_side_booking_repository.dart` (invalidation logic)

### Technical Patterns to Follow

1. **TDD**: Write failing tests first for each component
2. **Domain-Driven**: Business rules in domain layer, not UI
3. **Optimistic Updates**: Follow existing booking service patterns
4. **Error Handling**: Comprehensive exception handling with user-friendly messages
5. **State Management**: Use Riverpod patterns established in the codebase

### Business Logic Implementation

```dart
// Domain layer - business rules
class BookingCancellationRules {
  static bool canCancelBooking(
    Booking booking,
    PitchSettings pitchSettings,
    DateTime currentTime,
  ) {
    // Only confirmed or pending payment bookings can be cancelled
    if (!_isValidStatusForCancellation(booking.status)) {
      return false;
    }
    
    // Check if we're within the cancellation window
    final cancellationDeadline = booking.slotStartTime.subtract(
      Duration(hours: pitchSettings.cancellationWindowHours),
    );
    
    return currentTime.isBefore(cancellationDeadline);
  }
  
  static DateTime getCancellationDeadline(
    Booking booking,
    PitchSettings pitchSettings,
  ) {
    return booking.slotStartTime.subtract(
      Duration(hours: pitchSettings.cancellationWindowHours),
    );
  }
}
```

### UI/UX Requirements

1. **Cancel Button State**: 
   - Enabled for active bookings within cancellation window
   - Disabled with tooltip for expired cancellation window
   - Hidden for cancelled/completed bookings

2. **Confirmation Dialog**:
   - Show booking details
   - Display cancellation deadline
   - Warn about refund policy
   - Require explicit confirmation

3. **Error Handling**:
   - Clear error messages for different failure scenarios
   - Retry options where appropriate
   - Loading states during network operations

4. **Past Bookings**:
   - "Delete from view" option (local only)
   - Confirmation before deletion
   - No backend validation required

## Success Criteria

1. **Functional Requirements**:
   - ✅ Users can cancel bookings within the allowed window
   - ✅ Cancellation window is enforced on backend
   - ✅ Clear error messages for expired cancellation attempts
   - ✅ Past bookings can be deleted from local view
   - ✅ Real-time updates reflect cancellation immediately

2. **Technical Requirements**:
   - ✅ All tests pass (unit, widget, integration)
   - ✅ Business rules isolated in domain layer
   - ✅ Error handling covers all edge cases
   - ✅ Follows established architectural patterns
   - ✅ Performance doesn't degrade

3. **UX Requirements**:
   - ✅ Clear feedback for all user actions
   - ✅ Consistent with existing booking flow UX
   - ✅ Loading states for all async operations
   - ✅ Accessible and mobile-friendly

## Risk Mitigation

1. **Time Validation Edge Cases**: Use server time for validation to prevent client-side manipulation
2. **Race Conditions**: Use atomic updates and optimistic UI patterns
3. **Network Errors**: Implement retry logic and clear error recovery
4. **State Consistency**: Invalidate all related providers after cancellation
5. **User Experience**: Provide clear feedback and prevent accidental cancellations

## Next Steps

1. Start with Task 1.1 (Cancellation Business Rules Tests)
2. Follow TDD approach for each component
3. Test integration points between components
4. Validate complete user journey
5. Update documentation and deployment guides

---

**Note**: This implementation follows Iteration 3 goals from the project plan and maintains consistency with the established codebase architecture and patterns.
