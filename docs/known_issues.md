# Known Issues - skillz Football Pitch Booking App

**Last Updated**: July 15, 2025  
**Current Phase**: Frontend Integration & Code Quality Improvement

This document tracks current issues in the codebase, prioritized by severity and impact on functionality. Each issue includes a detailed plan for resolution.

## 📊 Issues Summary

| Priority | Count | Category | Status |
|----------|-------|----------|---------|
| 🔴 **Critical** | 0 | Runtime/Security Issues | None |
| 🟡 **High** | 4 | Maintainability/Bugs | Active |
| 🟠 **Medium** | 5 | Code Quality | Active |
| 🟢 **Low** | 3 | Cleanup/Minor | Active |
| **Total Active** | **12** | | |
| ✅ **Resolved** | 23+ | | Completed |

## 🔴 Critical Priority Issues (Fix Immediately)

### ✅ C1. Router Authentication Stream Issue (RESOLVED ✅)
**Status:** ✅ RESOLVED  
**Resolution Date:** July 10, 2025  
**Category:** Fixed Implementation  
**Impact:** Authentication state changes now properly trigger router refresh  
**Files Affected:**
- `lib/routing/app_router.dart` (line 32-50)

**Problem:** Router refresh mechanism was using deprecated `authStateChangesProvider.stream` API, which would only capture the first authentication event. Subsequent auth state changes wouldn't trigger router refresh.

**Resolution Applied:**
- Replaced deprecated `.stream` API with `ref.listen()` pattern
- Added proper `AsyncValue` handling in redirect logic
- Implemented manual stream controller for router refresh notifications
- Added proper error handling for loading and error states
- Priority: **Critical** - Authentication flow is fundamental to app security
- Estimated Effort: 30 minutes (completed)

---

### ✅ C2. Invalid Flutter Color API Usage (RESOLVED ✅)
**Status:** ✅ RESOLVED  
**Resolution Date:** July 10, 2025  
**Category:** Fixed Implementation  
**Impact:** Fixed runtime errors when using Color API methods  
**Files Affected:**
- `lib/features/booking/presentation/enhanced_booking_confirmation_screen.dart` (line 188-199)
- `lib/features/availability/presentation/widgets/slot_list_item.dart` (line 63-66)

**Problem:** Code was using deprecated `withOpacity()` method which is deprecated in Flutter 3.27+. The correct modern API is `withValues(alpha: x)`.

**Resolution Applied:**
- Replaced all `withOpacity(x)` calls with `withValues(alpha: x)` throughout the codebase
- Verified no remaining deprecated color API usage exists
- Confirmed `flutter analyze` shows no color-related warnings
- Tested app builds successfully without runtime exceptions
- Priority: **Critical** - Prevents runtime exceptions and uses modern Flutter API
- Estimated Effort: 30 minutes (completed)

---

### ✅ C3. Navigation Route Mismatch (RESOLVED ✅)
**Status:** ✅ RESOLVED  
**Resolution Date:** July 10, 2025  
**Category:** Fixed Implementation  
**Impact:** Fixed navigation failure and potential app crashes  
**Files Affected:**
- `lib/features/booking/presentation/booking_confirmation_screen.dart` (line 186)
- `lib/core/routing/route_constants.dart` (NEW - Central route management)
- Multiple navigation files throughout the app

**Problem:** Navigation was using inconsistent route paths (e.g., `/my_bookings` vs `/my-bookings`) due to hardcoded route strings scattered throughout the codebase.

**Resolution Applied:**
- Implemented comprehensive route constants system in `lib/core/routing/route_constants.dart`
- Replaced all hardcoded route strings with centralized constants (`AppRoutes.myBookingsPath`, etc.)
- Fixed `/my_bookings` vs `/my-bookings` inconsistency by using `AppRoutes.myBookingsPath = '/my-bookings'`
- Added both path constants (for `context.go()`) and name constants (for `context.goNamed()`)
- Updated all navigation calls throughout the app to use the new constants
- Maintained backwards compatibility with deprecated screen constants
- Priority: **Critical** - Navigation is fundamental to user experience
- Estimated Effort: 5 minutes (expanded to comprehensive route management - completed)

---

### ✅ C3. Booking Limit Enforcement Issue (RESOLVED ✅)
**Status:** ✅ RESOLVED  
**Resolution Date:** July 15, 2025  
**Category:** Configuration Issue - No Bug Found  
**Impact:** System working correctly - database configured properly  
**Files Affected:** None - Database configuration was correct

**Problem:** Users were reported to be hitting a booking limit at 3 slots instead of the configured limit stored in `pitch_settings.max_bookings` (verified to be 4).

**Investigation Results:**
- ✅ Database configuration: `pitch_settings.max_bookings_per_user = 4` (correct)
- ✅ User booking stats: Multiple users with `active_bookings_count = 4` (working correctly)
- ✅ Actual booking data: Shows 4 bookings per user as expected
- ✅ Edge Function logic: Correctly enforces 4-booking limit

**Resolution:** Issue could not be reproduced. Database and backend systems are working correctly with proper 4-slot booking limits. May have been a transient issue or user misunderstanding.

**Expected Behavior:** Users should be able to book up to `pitch_settings.max_bookings` slots ✅ **WORKING**
**Actual Behavior:** System correctly allows 4 bookings per user as configured ✅ **CONFIRMED**

---

### ✅ C4. Booking Status Classification and Display Issue (RESOLVED ✅)
**Status:** ✅ RESOLVED  
**Resolution Date:** July 15, 2025  
**Category:** Database Maintenance - Automatic System Deployed  
**Impact:** Booking status transitions now fully automated  
**Files Affected:** Database triggers and scheduled jobs

**Problem:** Bookings were being incorrectly categorized and displayed in wrong sections of the UI due to stale booking status data.

**Root Cause:** Automatic booking status transition system was not running on a schedule, causing old bookings to remain in "confirmed" status instead of transitioning to "completed".

**Investigation Results:**
- ✅ All old bookings (July 10-11) correctly transitioned to "completed" status
- ✅ `user_booking_stats.active_bookings_count` reset to 0 for all users
- ✅ `update_booking_states()` function exists and works correctly
- ✅ Database triggers properly maintain booking counts

**Resolution Applied:**
1. **Immediate Fix:** Applied manual status transition to clean up stale data
2. **Robust Solution:** Deployed `pg_cron` scheduled job to run `update_booking_states()` hourly
3. **Automatic Monitoring:** System now automatically transitions bookings without manual intervention

**Scheduled Job Details:**
- **Frequency:** Every hour at the top of the hour (`0 * * * *`)  
- **Function:** `update_booking_states()`
- **Status:** Active and operational

**Expected Behavior:**
- **Active/Current bookings**: Slots where current time is between start_time and end_time ✅ **WORKING**
- **Upcoming bookings**: Slots where start_time is in the future ✅ **WORKING**  
- **Past bookings**: Slots where end_time is in the past ✅ **WORKING**
- **Automatic transitions**: Bookings automatically move to "completed" status ✅ **AUTOMATED**

---

## 🟡 High Priority Issues (Fix Before Next Release)

### H1. Missing Generic Type Safety
**Status:** 🟡 HIGH  
**Category:** Redundant Code  
**Impact:** Code maintenance issues, potential divergence  
**Files Affected:**
- `lib/features/availability/application/real_time_availability_provider.dart` (line 17-33)
- `lib/features/availability/application/real_time_availability_simple.dart`

**Problem:** `RealTimeAvailabilityParams` is defined identically in two separate files, leading to code duplication and potential maintenance divergence.

**Resolution:**
- Consolidate parameter class into a shared file under `lib/features/availability/domain/`
- Update imports in both provider files
- Priority: **High** - Prevents code drift and maintenance issues
- Estimated Effort: 45 minutes

---

### H2. Duplicate Provider Implementation
**Status:** 🟡 HIGH  
**Category:** Redundant Code  
**Impact:** Conflicting provider names, runtime errors  
**Files Affected:**
- `lib/features/availability/application/real_time_availability_provider.dart` (line 91-114)
- `lib/features/availability/application/real_time_availability_simple.dart`

**Problem:** `OptimisticBookingState` is implemented in both files, creating potential for conflicting Riverpod provider names.

**Resolution:**
- Keep only one implementation of the optimistic booking state
- Remove duplicate from one of the files
- Priority: **High** - Prevents Riverpod provider conflicts
- Estimated Effort: 30 minutes

---

### H3. Duplicate Logger Utilities
**Status:** 🟡 HIGH  
**Category:** Redundant Code  
**Impact:** Confusion over which logger to use, inconsistent logging  
**Files Affected:**
- `lib/core/utils/logger.dart` (line 3-69)
- `lib/core/utils/logger_service.dart` (line 4-68)

**Problem:** Two different logger classes provide similar functionality and export global loggers, creating confusion and potential inconsistency.

**Resolution:**
- Standardize on one logging utility (preferably `logger_service.dart`)
- Migrate all imports to use the chosen logger
- Remove the redundant logger file
- Priority: **High** - Ensures consistent logging across the app
- Estimated Effort: 60 minutes

---

### H4. Missing Generic Type Safety
**Status:** 🟡 HIGH  
**Category:** Incomplete Implementation  
**Impact:** Type safety issues, potential runtime errors  
**Files Affected:**
- `lib/features/booking/presentation/my_bookings_screen.dart` (line 111-115)

**Problem:** `_BookingsList` stores bookings as `List` without generic type, reducing type safety.

**Resolution:**
- Change to `List<Booking>` for proper type safety
- Update related code to handle typed list properly
- Priority: **High** - Type safety prevents runtime errors
- Estimated Effort: 15 minutes

---

### H2. Incomplete Router Parameter Extraction
**Status:** 🟡 HIGH  
**Category:** Incomplete Implementation  
**Impact:** Potential parameter parsing errors  
**Files Affected:**
- `lib/routing/app_router.dart` (line 117)

**Problem:** TODO comment indicates ad-hoc parameter parsing that should be replaced with validated approach for `BookingConfirmationScreen`.

**Resolution:**
- Implement proper parameter validation and extraction
- Add error handling for malformed parameters
- Priority: **High** - Prevents navigation errors
- Estimated Effort: 90 minutes

---

### H3. Deprecated Service Cleanup
**Status:** 🟡 HIGH  
**Category:** Incomplete Implementation  
**Impact:** Code bloat, potential confusion  
**Files Affected:**
- `lib/features/booking/application/booking_service.dart` (line 20)

**Problem:** File contains TODO indicating it should be removed after test migration, but still exists in codebase.

**Resolution:**
- Verify tests no longer depend on this service
- Remove deprecated service file
- Update any remaining imports
- Priority: **High** - Removes dead code and potential confusion
- Estimated Effort: 30 minutes

---

### H4. Missing Authentication Methods
**Status:** 🟡 HIGH  
**Category:** Incomplete Implementation  
**Impact:** Limited authentication options  
**Files Affected:**
- `lib/features/auth/application/auth_service.dart` (line 87)

**Problem:** TODO remains for adding password reset or social login methods, limiting authentication functionality.

**Resolution:**
- Implement password reset functionality
- Consider adding social login options (Google, Apple)
- Remove TODO or implement planned features
- Priority: **High** - Essential user functionality
- Estimated Effort: 4-6 hours

---

## 🟠 Medium Priority Issues (Fix During Next Iteration)

### M1. Frontend Integration Testing Required
**Status:** � MEDIUM  
**Category:** Integration Issue  
**Impact:** Backend is complete, need to validate frontend integration  
**Files Affected:**
- `.env` file (needs new Supabase credentials)
- API integration points throughout Flutter app

**Problem:** Backend has been completely reconstructed with new Supabase instance, requiring frontend integration testing.

**Resolution Plan:**
1. Update `.env` with new Supabase URL and anon key
2. Test API connectivity and data flow
3. Validate pitch enable/disable functionality
4. Verify booking flow with new backend
5. Test real-time slot updates
- Priority: **Medium** - System operational, integration validation needed
- Timeline: July 9-10, 2025

---

### M2. Missing Booking Cancellation
**Status:** 🟠 MEDIUM  
**Category:** Incomplete Implementation  
**Impact:** Users cannot cancel bookings  
**Files Affected:**
- `lib/features/booking/presentation/my_bookings_screen.dart` (line 191)

**Problem:** Placeholder comment "TODO: Implement booking cancellation" with unused button in UI.

**Resolution:**
- Implement booking cancellation logic in backend
- Add cancellation validation (time limits, policies)
- Connect UI button to cancellation service
- Add confirmation dialog
- Priority: **Medium** - Important user feature but not blocking
- Estimated Effort: 3-4 hours

---

### M3. Inconsistent Print Statement Usage
**Status:** 🟠 MEDIUM  
**Category:** Code Quality Issue  
**Impact:** Inconsistent debugging, poor logging practices  
**Files Affected:**
- `lib/features/booking/presentation/booking_confirmation_screen.dart` (line 357)

**Problem:** TODO to replace print with proper logging service persists, indicating inconsistent logging practices.

**Resolution:**
- Replace all remaining print statements with proper logger
- Ensure consistent logging throughout application
- Priority: **Medium** - Code quality improvement
- Estimated Effort: 45 minutes

---

### M4. Performance Monitoring Setup
**Status:** 🟠 MEDIUM  
**Category:** Monitoring Gap  
**Impact:** System operational, monitoring needed for optimization  
**Files Affected:** TBD

**Problem:** Need to set up monitoring for slot query performance and real-time updates.

**Resolution Plan:**
1. Implement query performance tracking
2. Set up error monitoring and alerting
3. Create performance benchmarks
4. Monitor slot generation performance under load
- Priority: **Medium** - Optimization rather than functionality
- Timeline: July 15, 2025

---

### M5. Database Schema Verification Needed
**Status:** 🟠 MEDIUM  
**Category:** Configuration Verification  
**Impact:** Dynamic settings may fall back to hardcoded values  
**Files Affected:**
- `docs/database_migrations/002_add_pricing_and_limits_to_pitch_settings.sql`
- Production Supabase database

**Problem:** Migration script exists for dynamic pricing and booking limits, but uncertain if applied to production database.

**Resolution Plan:**
1. Check current production database schema in Supabase
2. Apply migration script if pricing fields are missing
3. Verify all pitch settings have proper default values
4. Test dynamic loading of settings in application
- Priority: **Medium** - Affects configuration flexibility
- Estimated Effort: 2 hours

---

## 🟢 Low Priority Issues (Fix When Convenient)

### L1. Hardcoded Booking Limits in UI
**Status:** 🟢 LOW  
**Category:** Configuration Issue  
**Impact:** Booking limits cannot be configured per-pitch  
**Files Affected:**
- `lib/features/availability/presentation/widgets/slot_list_item.dart` (hardcoded limit of 4)

**Problem:** UI logic uses hardcoded booking limit of 4 instead of loading from dynamic settings.

**Resolution:**
- Update `SlotListItem` to use `maxBookingsPerUser` from pitch settings
- Remove all hardcoded "4" references in booking limit logic
- Test with different pitch configurations
- Priority: **Low** - Functionality works, just not configurable
- Estimated Effort: 60 minutes

---

### H1. Over-Mocking Anti-Patterns in Test Suite
**Status:** 🔴 HIGH
**Category:** Test Quality & Technical Debt
**Impact:** Critical - Tests hide business logic bugs, undermining codebase confidence
**Files Affected:**
- `test/features/booking/application/booking_cancellation_service_test.dart:16,67,77`
- `test/investigation/real_time_booking_flow_test.dart:30-61`
- `test/features/booking/presentation/booking_confirmation_screen_test.dart:12-24`
- `test/features/availability/presentation/booking_limit_widget_test.dart:132-134`
- Multiple provider override anti-patterns across test suite

**Problem:** Test suite systematically mocks business logic classes (`TimeService`, `BookingBusinessRules`, `ServerSideBookingRepository`) instead of testing them directly. This creates dangerous scenarios where tests pass even when business logic is broken.

**Specific Violations:**
- **MockTimeService**: Hides time-based business logic bugs in cancellation and booking flows
- **MockServerSideBookingRepository**: Bypasses repository business logic and validation
- **MockOptimisticBookingService**: Mocks core booking business logic completely
- **Provider Overrides**: Excessive overrides bypass business logic execution

**Resolution Plan:**
- **Phase 1**: Delete harmful tests that provide negative value (Week 1)
- **Phase 2**: Refactor critical business logic tests to use real implementations (Week 2)
- **Phase 3**: Enhance integration test coverage with real database testing (Week 3)
- **Phase 4**: Improve provider testing patterns to minimize overrides (Week 4)
- **Documentation**: Complete refactoring plan available in `docs/test_refactoring_plan.md`
- Priority: **High** - Affects confidence in business logic correctness
- Estimated Effort: 4 weeks
- **Remediation Timeline**: Q1 2025

---

### L2. Test Code Quality Issues
**Status:** 🟢 LOW
**Category:** Test Quality
**Impact:** Code consistency in tests
**Files Affected:**
- `test/features/availability/presentation/booking_limit_widget_test.dart:177,179,188`
- `test/test_helpers/supabase_mock_helper.dart:273,322`
- `test/test_helpers/supabase_mock_helper.dart:313,314`

**Problem:** Test files contain print() statements instead of proper logging and UPPER_CASE variable names instead of lowerCamelCase.

**Resolution:**
- Replace `print()` calls with `debugPrint()` or remove
- Rename UPPER_CASE variables to lowerCamelCase
- Run `flutter analyze` to catch similar issues
- Priority: **Low** - Test quality, doesn't affect functionality
- Estimated Effort: 30 minutes
- **Note**: Will be addressed as part of H1 test refactoring initiative

---

### L3. Missing Test Dependencies
**Status:** 🟢 LOW  
**Category:** Test Environment  
**Impact:** Potential test environment inconsistency  
**Files Affected:**
- `test/test_helpers/widget_test_helper.dart:5`

**Problem:** Test file imports `shared_preferences` package which isn't declared in dev_dependencies.

**Resolution:**
- Add `shared_preferences` to dev_dependencies in pubspec.yaml
- Review all test imports for missing dependencies
- Ensure test environment matches production dependencies
- Priority: **Low** - Tests currently work, but dependency should be explicit
- Estimated Effort: 15 minutes

---

## 📋 Issue Categories Reference

### Category Definitions:
- **Flawed Implementation**: Code that contains logical errors or uses incorrect APIs
- **Redundant Code**: Duplicate implementations that create maintenance burden
- **Incomplete Implementation**: Features or fixes that are partially implemented
- **Integration Issue**: Problems related to system integration and configuration
- **Code Quality**: Style, consistency, and best practice violations
- **Test Quality**: Issues specific to test code and testing infrastructure

### Priority Justifications:
- **🔴 Critical**: Runtime errors, security vulnerabilities, broken core functionality
- **🟡 High**: Maintainability issues, potential bugs, incomplete core features
- **🟠 Medium**: Code quality, configuration issues, non-blocking features
- **🟢 Low**: Minor improvements, test quality, cleanup tasks

---

## 🎯 Next Steps & Action Plan

### Immediate Actions (Next 1-2 Days):
1. **Address High Priority H1-H3** - Code maintenance and quality (redundant code cleanup)
2. **Complete Frontend Integration (M1)** - System validation
3. **Focus on Code Quality** - No critical issues remaining!

### Sprint Planning (Next 2 Weeks):
1. **Week 1**: High priority issues (H1-H4) - Code quality and maintenance
2. **Week 2**: Remaining High priority and Medium priority issues (H5-H7, M1-M3)

### Ongoing Maintenance:
- **Weekly Code Reviews**: Check for new instances of identified patterns
- **Automated Linting**: Set up pre-commit hooks for code quality
- **Regular Audits**: Monthly review of this document and new issue identification

---

## ✅ Recently Resolved Issues (Reference)

### ~~1. 3-Slot Offset Bug~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Implemented ADR-007 database-centric slot management, eliminating client-side calculations that caused the offset.

### ~~2. Backend Infrastructure Failure~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Complete Supabase database reconstruction with improved schema and functionality.

### ~~3. Flutter Color API Usage~~ (RESOLVED ✅)
**Resolution Date:** July 10, 2025  
**Solution:** Replaced all deprecated `withOpacity()` calls with modern `withValues(alpha: x)` API throughout the codebase.

### ~~4. Route Constants Implementation~~ (RESOLVED ✅)
**Resolution Date:** July 10, 2025  
**Solution:** Implemented comprehensive route constants system with centralized route management and fixed navigation path inconsistencies.

### ~~5. Pitch Enable/Disable Functionality Missing~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Added `is_enabled` field to pitch_settings with proper RLS policies and slot generation validation.

### ~~6. Database Race Conditions~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Implemented database constraints, triggers, and exclusion constraints to prevent race conditions.

### ~~7. Date Filtering Logic Timezone Bug~~ (RESOLVED ✅)
**Resolution Date:** January 5, 2025  
**Solution:** Fixed timezone conversion issues in availability system using TimeService UTC methods.

### ~~8. Flutter Deprecated API Usage~~ (RESOLVED ✅)
**Resolution Date:** Recent  
**Solution:** Replaced deprecated APIs including MaterialState with WidgetState, withOpacity with withValues.

---

## 📈 Issue Resolution History

| Month | Issues Resolved | Major Fixes |
|-------|----------------|-------------|
| July 2025 | 8+ | Route Constants System, Flutter Color API fix, Backend reconstruction, ADR-007 implementation |
| June 2025 | 8 | Test infrastructure, race conditions, build errors |
| May 2025 | 5 | Authentication, booking flow, UI components |

---

## 🎯 Monitoring and Prevention

### Automated Quality Checks:
1. **Pre-commit Hooks:** Set up `flutter analyze` and basic linting checks
2. **CI/CD Pipeline:** Add automated testing for duplicate providers and missing constants
3. **Code Review Checklist:** Include checks for deprecated API usage and proper error handling

### Documentation Maintenance:
- **Weekly Updates:** Review and update this document as issues are resolved
- **Monthly Audits:** Comprehensive review of codebase for new issues
- **Quarterly Reviews:** Assess overall code quality trends and improvement areas

---

## 🚫 Non-Issues (Clarifications)

### Build/Compilation Issues
**Status:** ✅ All Clear  
**Last Check:** July 8, 2025  
**Note:** All previous build and import errors have been resolved.

### Test Infrastructure
**Status:** ✅ Stable  
**Last Check:** July 2025  
**Note:** Test infrastructure is working correctly with comprehensive provider overrides.

---

## ✅ RESOLVED ISSUES

### ✅ Supabase Signup Fails in Release APK (FIXED)
**Status:** RESOLVED  
**Priority:** P1 (Critical)  
**Category:** Build Configuration  

**Problem:** Supabase signup worked in debug builds but failed silently in release APKs.

**Root Cause:** Missing `INTERNET` permission in AndroidManifest.xml. Debug builds had implicit network access, but release builds require explicit permissions.

**Solution Applied:**
1. ✅ Added `<uses-permission android:name="android.permission.INTERNET" />` to AndroidManifest.xml
2. ✅ Disabled ProGuard minification (`isMinifyEnabled = false`) to prevent Supabase class obfuscation
3. ✅ Verified `.env` file is properly included in assets via pubspec.yaml

**Files Modified:**
- `android/app/src/main/AndroidManifest.xml` (added INTERNET permission)
- `android/app/build.gradle.kts` (disabled minification for release builds)

**Testing:** Build and test the new release APK to confirm Supabase signup now works.

---

### ✅ iOS Application Configuration for Supabase Signup (CONFIGURED)
**Status:** CONFIGURED  
**Priority:** P1 (Critical)  
**Category:** iOS Build Configuration  

**Setup Completed:**
1. ✅ **App Name Consistency**: Fixed CFBundleName to "Skillz" (was inconsistent)
2. ✅ **Bundle Identifier**: Correctly set to `com.mw.skillz` 
3. ✅ **Deep Linking**: Configured for `io.supabase.skillz://login-callback`
4. ✅ **Network Security**: Added App Transport Security (ATS) configuration for Supabase
5. ✅ **Environment Variables**: `.env` file properly included in assets

**Network Configuration Added:**
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <false/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>supabase.co</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
            <key>NSIncludesSubdomains</key>
            <true/>
        </dict>
    </dict>
</dict>
```

**Files Modified:**
- `ios/Runner/Info.plist` (app name consistency, network security, deep linking)

**Next Steps for Building iOS:**
1. Open Xcode: `open ios/Runner.xcworkspace`
2. Configure signing certificate and provisioning profile
3. Select development team under Signing & Capabilities
4. Build for device or simulator

**Note:** Unlike Android which requires explicit INTERNET permission, iOS apps have network access by default. The ATS configuration ensures secure HTTPS connections to Supabase.

---
