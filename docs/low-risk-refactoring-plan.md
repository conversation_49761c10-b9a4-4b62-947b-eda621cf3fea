# lib/features/availability/application/real_time_availability_provider.dart 
# - 
- (line 17–33): RealTimeAvailabilityParams is defined here and an identical class exists in real_time_availability_simple.dart.
- Category: Redundant
- Suggestion: Consolidate the parameter class into a single shared file to avoid maintenance divergence.

# lib/features/availability/application/real_time_availability_provider.dart 
- (line 91–114): OptimisticBookingState is implemented here, but the same provider appears in real_time_availability_simple.dart.
Category: Redundant
- Suggestion: Keep only one implementation of the optimistic booking state to prevent conflicting provider names.

# lib/core/utils/logger.dart 
- (line 3–69) and lib/core/utils/logger_service.dart # 
- (line 4–68): Two different logger classes provide similar functionality and export a global logger.
Category: Redundant
- Suggestion: Standardize on one logging utility to avoid confusion over which logger to use.

# ✅ lib/routing/app_router.dart (RESOLVED)
- (line 32–38): goRouterRefreshStreamProvider was using deprecated authStateChangesProvider.stream API.
- Category: Fixed
- Resolution: Replaced with ref.listen() pattern and proper AsyncValue handling for router refresh notifications.

# ✅ lib/features/booking/presentation/enhanced_booking_confirmation_screen.dart 
- (line 188–199) and lib/features/availability/presentation/widgets/slot_list_item.dart # 
- (line 63–66): Calls to Color.withValues are used, but Color in Flutter has withOpacity/withAlpha—no withValues.
- Category: Flawed
- Suggestion: Replace these calls with the proper Flutter color API.

# lib/features/booking/presentation/booking_confirmation_screen.dart 
- (line 186): Navigation uses context.go('/my_bookings'), but the route is defined as /my-bookings.
- Category: Flawed
- Suggestion: Update the navigation path to match the route definition.

# lib/features/booking/presentation/my_bookings_screen.dart 
- (line 111–115): _BookingsList stores bookings as List without a generic type.
Category: Incomplete
- Suggestion: Use List<Booking> for type safety.

# lib/features/booking/application/booking_service.dart 
- (line 20): Deprecated service contains a TODO indicating it should be removed after test migration.
Category: Incomplete
- Suggestion: Delete this file when tests no longer rely on it.

# lib/features/booking/presentation/my_bookings_screen.dart 
- (line 191): Placeholder comment // TODO: Implement booking cancellation.
Category: Incomplete
- Suggestion: Implement cancellation logic or remove the unused button.

# lib/routing/app_router.dart 
- (line 117): Comment notes “TODO: Extract parameters safely and pass them to BookingConfirmationScreen”.
Category: Incomplete
- Suggestion: Replace the ad‑hoc parameter parsing with a validated approach.

# lib/features/auth/application/auth_service.dart 
- (line 87): TODO remains for adding password reset or social login methods.
Category: Incomplete
- Suggestion: Either implement additional authentication flows or remove the placeholder.

# lib/features/booking/presentation/booking_confirmation_screen.dart 
- (line 357): TODO to replace print with a proper logging service persists.
Category: Incomplete
- Suggestion: Swap remaining print/debug statements for the standardized logger.

# Fixes

# 1. Consolidate RealTimeAvailabilityParams class
- Issue: Duplicate parameter class exists in real_time_availability_provider.dart and real_time_availability_simple.dart

- Direction:

Create a single file (e.g., lib/features/availability/application/real_time_availability_params.dart) containing RealTimeAvailabilityParams.

Export this file and update both providers to import it.

Delete the duplicate class definitions.

- Suggested task
Refactor RealTimeAvailabilityParams


# 2. Keep only one OptimisticBookingState provider
- Issue: OptimisticBookingState implemented twice (lines 91–114 in real_time_availability_provider.dart and lines 82–109 in real_time_availability_simple.dart)

- Direction:

Decide which file should own the provider (prefer the simpler one or whichever is referenced by the rest of the app).

Move the chosen implementation into a dedicated file (e.g., optimistic_booking_state.dart) if needed.

Remove the duplicate implementation and adjust imports.

- Suggested task
Deduplicate OptimisticBookingState provider


# 3. Standardize logging utilities
- Issue: Two logger implementations (logger.dart and logger_service.dart) exporting global logger instances

- Direction:

Select one logging approach (likely logger_service.dart since it wraps the logger package).

Remove the redundant file or merge features if needed.

Update imports across the project to use the single logger.

- Suggested task
Unify logging


# 4. Fix auth stream listening in router
- Issue: goRouterRefreshStreamProvider converts a Future to a stream, causing only one auth change to trigger refresh

- Direction:

Replace .read(authStateChangesProvider.future).asStream() with ref.watch(authStateChangesProvider.stream).

Verify that the router rebuilds on every auth state change.

- Suggested task
Use authStateChangesProvider.stream


# 5. Replace invalid Color.withValues calls
- Issue: .withValues isn’t a valid Flutter API; appears in slot list items and enhanced booking confirmation

- Direction:

Replace color.withValues(alpha: x) with either color.withOpacity(x) or color.withAlpha((x * 255).round()).

Review other occurrences (lines 138–144 and 203 in slot_list_item.dart) and update similarly.

- Suggested task
Correct color API usage


# 6. Correct navigation path to My Bookings
- Issue: Navigation uses /my_bookings while the router defines /my-bookings

- Direction:

Replace all occurrences of /my_bookings with /my-bookings.

Ensure tests cover this route.

- Suggested task
Fix My Bookings route


# 7. Specify generic type for bookings list
- Issue: _BookingsList uses final List bookings; without a type

- Direction:

Replace with final List<Booking> bookings; (import the booking model if needed).

Adjust any calling code to pass typed lists.

- Suggested task
Add generic type to bookings list


# 8. Remove deprecated booking_service.dart
- Issue: File is retained only for legacy tests and marked with TODO for removal

- Direction:

Confirm tests have migrated to use OptimisticBookingService.

Delete booking_service.dart and remove its references.

- Suggested task
Delete deprecated booking service


# 9. Implement or remove booking cancellation
- Issue: Placeholder TODO inside _BookingsList item action

- Direction:

Decide on cancellation flow (call a repository method, update UI, handle errors).

Implement the logic or remove the button until implemented.

- Suggested task
Handle booking cancellation


# 10. Parse booking confirmation parameters safely
- Issue: TODO comment in router about extracting query parameters safely

- Direction:

Create a small model or utility to parse and validate parameters (pitch ID, times).

Replace manual DateTime.tryParse calls with this utility.

Redirect or show an error if parsing fails.

- Suggested task
Validate booking confirmation parameters


# 11. Complete authentication methods or remove TODO
- Issue: TODO in auth_service.dart suggests adding password reset or social login methods

- Direction:

Either implement additional methods (e.g., sendPasswordResetEmail, social providers) or remove the comment if not planned.

Update documentation accordingly.

- Suggested task
Finalize additional auth methods


# 12. Replace remaining print/debugPrint statements
- Issue: Debug print used instead of logger in BookingConfirmationScreen

- Direction:

Swap debugPrint with logger.e/logger.w as appropriate.

Search the repo for other stray prints and replace them.

- Suggested task
Use logger instead of print


# 13. Navigate using the TimeSlotInfo object (optional cleanup)
- Issue: TODO at the top of BookingConfirmationScreen suggests passing a slot object directly

- Direction:

Add a constructor parameter accepting a TimeSlotInfo instance.

Adjust call sites and router parameter parsing accordingly.

- Suggested task
Consider passing TimeSlotInfo directly


Following these tasks will consolidate duplicate logic, fix minor flaws, and complete missing functionality across the project. After each change, run flutter analyze and flutter test to ensure code quality and correctness.