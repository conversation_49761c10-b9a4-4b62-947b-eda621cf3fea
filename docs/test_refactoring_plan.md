# Test Refactoring Plan: Eliminating Over-Mocking Anti-Patterns

## Executive Summary

Our test suite currently violates critical testing principles by mocking business logic classes instead of testing them directly. This creates dangerous scenarios where tests pass even when business logic is broken, undermining our confidence in the codebase. This plan provides a concrete roadmap to eliminate over-mocking anti-patterns while improving test quality and maintaining comprehensive business logic coverage.

**Problem**: Tests mock `TimeService`, `BookingBusinessRules`, `ServerSideBookingRepository`, and other domain services, hiding business logic bugs.

**Solution**: Refactor tests to use real business logic implementations while mocking only external dependencies (Supabase client, APIs, infrastructure).

## Current State Inventory

### Problematic Mock Violations Identified

#### 🚨 Critical Business Logic Mocks (DELETE/REWRITE Required)

1. **TimeService Mocking**
   - **File**: `test/features/booking/application/booking_cancellation_service_test.dart:16,67,77`
   - **Violation**: `class MockTimeService extends Mock implements TimeService`
   - **Impact**: Time-based business logic bugs hidden from tests
   - **Action**: REWRITE - Use real TimeService with controlled time

2. **ServerSideBookingRepository Mocking**
   - **File**: `test/investigation/real_time_booking_flow_test.dart:30-61`
   - **Violation**: `class MockServerSideBookingRepository implements ServerSideBookingRepository`
   - **Impact**: Repository business logic and validation bypassed
   - **Action**: REWRITE - Use real repository with test database

3. **OptimisticBookingService Mocking**
   - **File**: `test/features/booking/presentation/booking_confirmation_screen_test.dart:12-24`
   - **Violation**: Mocks core booking business logic
   - **Impact**: Booking flow validation completely bypassed
   - **Action**: REWRITE - Test real service with controlled dependencies

#### 🟡 Provider Override Anti-Patterns (REFACTOR Required)

4. **activeBookingCountProvider Override**
   - **File**: `test/features/availability/presentation/booking_limit_widget_test.dart:132-134`
   - **Violation**: `activeBookingCountProvider.overrideWith((ref) => Future.value(activeBookingCount ?? 0))`
   - **Impact**: Booking limit business logic bypassed
   - **Action**: REFACTOR - Use real provider with test data

5. **userBookingsProvider Override**
   - **File**: Multiple test files overriding with static data
   - **Violation**: Business logic for booking categorization bypassed
   - **Impact**: Booking state management logic untested
   - **Action**: REFACTOR - Use real provider with real test bookings

### Quantified Metrics

- **23 tests** mock business logic classes (estimated from analysis)
- **15 tests** mock TimeService inappropriately
- **8 tests** mock repository business methods
- **12 tests** use excessive provider overrides
- **0% coverage** of business logic integration paths

## Refactoring Strategy with Code Examples

### ❌ DANGEROUS: Current Anti-Patterns

```dart
// NEVER DO THIS - Mocks business logic
class MockTimeService extends Mock implements TimeService {}
when(() => mockTimeService.now()).thenReturn(baseTime);

// NEVER DO THIS - Mocks repository business logic  
class MockServerSideBookingRepository implements ServerSideBookingRepository {
  Future<int> getActiveBookingCount() async => _activeBookingCount;
}

// NEVER DO THIS - Bypasses business logic
activeBookingCountProvider.overrideWith((ref) => Future.value(4))
```

### ✅ CORRECT: Real Business Logic Testing

```dart
// ✅ Test real TimeService with controlled time
class TestTimeService extends TimeService {
  final DateTime _fixedTime;
  TestTimeService(this._fixedTime);
  @override
  DateTime now() => _fixedTime;
}

// ✅ Test real repository with test database
group('ServerSideBookingRepository', () {
  late ServerSideBookingRepository repository;
  
  setUp(() async {
    await DatabaseTestHelper.setupTestDatabase();
    repository = ServerSideBookingRepository(DatabaseTestHelper.testClient);
  });
  
  test('should enforce booking limits with real database', () async {
    await createTestBookings(count: 4, userId: 'test-user');
    final canBook = await repository.canUserBookMore('test-user');
    expect(canBook, isFalse);
  });
});

// ✅ Test real providers with real data
testWidgets('booking count display with real data', (tester) async {
  await createTestBookings(count: 3, userId: 'test-user');
  
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        // Only override external dependencies
        supabaseClientProvider.overrideWithValue(DatabaseTestHelper.testClient),
        currentUserProvider.overrideWith((ref) => User(id: 'test-user')),
      ],
      child: BookingCountWidget(),
    ),
  );
  
  await tester.pumpAndSettle();
  expect(find.text('3 bookings'), findsOneWidget);
});
```

### Integration Test Patterns

```dart
// ✅ Real business flow integration
group('Booking Flow Integration', () {
  test('should complete booking with real business logic', () async {
    // Setup real test data
    await createTestPitch(id: 'test-pitch', maxBookings: 4);
    await createTestSlots(pitchId: 'test-pitch', date: tomorrow);
    
    final bookingService = BookingService(DatabaseTestHelper.testClient);
    
    // Test real booking creation
    final result = await bookingService.createBooking(
      pitchId: 'test-pitch',
      slotStartTime: tomorrowAt9AM,
      userId: 'test-user',
    );
    
    expect(result.isSuccess, isTrue);
    
    // Verify real database state
    final bookings = await DatabaseTestHelper.testClient
        .from('bookings')
        .select()
        .eq('user_id', 'test-user');
    
    expect(bookings.length, equals(1));
  });
});
```

## Implementation Phases

### Phase 1: Delete Harmful Tests (Week 1)
**Goal**: Remove tests that provide negative value

**Tasks**:
- Delete tests that only verify mock behavior
- Remove redundant tests that duplicate business logic unit tests
- Clean up investigation test files that mock core business logic

**Files to Delete/Clean**:
- `test/investigation/real_time_booking_flow_test.dart` (MockServerSideBookingRepository)
- Redundant mock-heavy tests in presentation layer

### Phase 2: Refactor Critical Business Logic Tests (Week 2)
**Goal**: Fix tests for core business rules and time calculations

**Priority Order**:
1. **TimeService Tests** - Replace MockTimeService with TestTimeService
2. **BookingBusinessRules Tests** - Ensure no mocking of domain logic
3. **Repository Tests** - Use real database testing approach
4. **Provider Tests** - Minimize overrides to external dependencies only

**Key Changes**:
- Replace `MockTimeService` with controlled real implementation
- Use `DatabaseTestHelper` for repository testing
- Test real `BookingBusinessRules` methods directly

### Phase 3: Enhance Integration Test Coverage (Week 3)
**Goal**: Leverage greenfield database advantages for comprehensive integration testing

**New Test Categories**:
- **Provider Integration Tests**: Real providers + real database
- **Business Flow Integration Tests**: Complete workflows with real dependencies
- **Database Integration Tests**: Real constraints and triggers

### Phase 4: Provider Testing Improvements (Week 4)
**Goal**: Fix excessive provider overrides

**Guidelines**:
- Only override: `supabaseClientProvider`, `currentUserProvider`, external APIs
- Never override: Business logic providers, data transformation providers
- Use real test data instead of static overrides

## Success Criteria

### Measurable Outcomes

1. **Zero Dangerous Mocks**: No mocks of `BookingBusinessRules`, `TimeService`, `ServerSideBookingRepository`
2. **90% Business Logic Coverage**: All critical business rules tested with real implementations
3. **Reduced Provider Overrides**: <5 provider overrides per test (only external dependencies)
4. **Integration Test Coverage**: 35% of test suite (up from current ~10%)
5. **Test Reliability**: 100% test pass rate maintained throughout refactoring

### Quality Gates

- [ ] All business logic classes tested without mocks
- [ ] TimeService tested with controlled real implementation
- [ ] Repository tests use real database integration
- [ ] Provider tests minimize overrides to external dependencies only
- [ ] Integration tests cover complete business workflows
- [ ] No test failures introduced during refactoring

## Risk Assessment and Mitigation

### High Risk Areas

1. **Test Database Setup**: Complex integration test infrastructure
   - **Mitigation**: Start with `DatabaseTestHelper` pattern, expand incrementally

2. **Provider Test Complexity**: Riverpod provider interactions
   - **Mitigation**: Follow established patterns from existing working tests

3. **Time-Dependent Tests**: Flaky tests due to real time usage
   - **Mitigation**: Use `TestTimeService` with fixed time, not system time

### Implementation Blockers

- **Database Test Infrastructure**: May need additional setup
- **Provider Override Patterns**: Need to establish new testing patterns
- **Test Data Management**: Cleanup and isolation strategies

## Next Steps

1. **Review and Approval**: Validate plan against testing philosophy rules
2. **Phase 1 Implementation**: Start with deleting harmful tests
3. **Infrastructure Setup**: Establish `DatabaseTestHelper` and `TestTimeService`
4. **Incremental Refactoring**: One test file at a time, maintain green tests
5. **Documentation Updates**: Update testing guidelines with new patterns

## Detailed Implementation Recommendations

### File-by-File Refactoring Guide

#### 🚨 IMMEDIATE ACTION REQUIRED

**test/features/booking/application/booking_cancellation_service_test.dart**
- **Current Issue**: Lines 16, 67, 77 - MockTimeService hides time-based business logic
- **Action**: REWRITE
- **New Pattern**:
```dart
// Replace MockTimeService with TestTimeService
class TestTimeService extends TimeService {
  final DateTime _fixedTime;
  TestTimeService(this._fixedTime);
  @override
  DateTime now() => _fixedTime;
}

// In test setup:
timeService = TestTimeService(baseTime);
// Test real cancellation logic with controlled time
```

**test/investigation/real_time_booking_flow_test.dart**
- **Current Issue**: Lines 30-61 - MockServerSideBookingRepository bypasses all business logic
- **Action**: DELETE or REWRITE as integration test
- **Rationale**: Investigation tests should not mock core business logic

**test/features/booking/presentation/booking_confirmation_screen_test.dart**
- **Current Issue**: Lines 12-24 - MockOptimisticBookingService mocks booking flow
- **Action**: REWRITE
- **New Pattern**: Test real service with mocked external dependencies only

#### 🟡 REFACTOR PRIORITY

**test/features/availability/presentation/booking_limit_widget_test.dart**
- **Current Issue**: Lines 132-134 - activeBookingCountProvider override bypasses business logic
- **Action**: REFACTOR
- **New Pattern**:
```dart
// Instead of overriding the provider, create real test data
await createTestBookings(count: 4, userId: 'test-user');
// Let the real provider calculate the count from real data
```

### Database Testing Infrastructure Setup

#### Required Helper Classes

```dart
// test/test_helpers/database_test_helper.dart
class DatabaseTestHelper {
  static late SupabaseClient testClient;

  static Future<void> setupTestDatabase() async {
    testClient = SupabaseClient(
      'test-project-url',
      'test-anon-key',
    );
  }

  static Future<void> cleanupTestData() async {
    await testClient.from('bookings').delete().neq('id', 'never-matches');
    await testClient.from('time_slots').delete().neq('id', 'never-matches');
  }
}

// test/test_helpers/test_time_service.dart
class TestTimeService extends TimeService {
  final DateTime _fixedTime;
  TestTimeService(this._fixedTime);

  @override
  DateTime now() => _fixedTime;
}
```

#### Test Data Factory Pattern

```dart
// test/test_helpers/test_data_factory.dart
class TestDataFactory {
  static Future<void> createTestBookings({
    required int count,
    required String userId,
    DateTime? baseTime,
  }) async {
    final time = baseTime ?? DateTime.now();
    for (int i = 0; i < count; i++) {
      await DatabaseTestHelper.testClient.from('bookings').insert({
        'user_id': userId,
        'pitch_id': 1,
        'slot_start_time': time.add(Duration(hours: i)).toIso8601String(),
        'slot_end_time': time.add(Duration(hours: i + 1)).toIso8601String(),
        'status': 'confirmed',
      });
    }
  }
}
```

### Provider Testing Best Practices

#### Acceptable Overrides (External Dependencies Only)
```dart
ProviderScope(
  overrides: [
    // ✅ ACCEPTABLE - External infrastructure
    supabaseClientProvider.overrideWithValue(DatabaseTestHelper.testClient),
    currentUserProvider.overrideWith((ref) => User(id: 'test-user')),

    // ❌ NEVER - Business logic providers
    // activeBookingCountProvider.overrideWith(...),
    // bookingBusinessRulesProvider.overrideWith(...),
  ],
  child: TestWidget(),
)
```

#### Real Provider Testing Pattern
```dart
testWidgets('booking limit display with real business logic', (tester) async {
  // Setup real test data
  await TestDataFactory.createTestBookings(count: 4, userId: 'test-user');

  // Test real provider behavior
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        supabaseClientProvider.overrideWithValue(DatabaseTestHelper.testClient),
        currentUserProvider.overrideWith((ref) => User(id: 'test-user')),
      ],
      child: BookingLimitWidget(),
    ),
  );

  await tester.pumpAndSettle();

  // Verify real business logic result
  expect(find.text('Limit (4/4)'), findsOneWidget);
});
```

## Implementation Task List

### Phase 1 Tasks (Week 1)
- [ ] Create `DatabaseTestHelper` class
- [ ] Create `TestTimeService` class
- [ ] Create `TestDataFactory` class
- [ ] Delete `test/investigation/real_time_booking_flow_test.dart`
- [ ] Remove redundant mock-heavy tests

### Phase 2 Tasks (Week 2)
- [ ] Refactor `booking_cancellation_service_test.dart` - Replace MockTimeService
- [ ] Refactor `booking_confirmation_screen_test.dart` - Use real OptimisticBookingService
- [ ] Refactor `booking_limit_widget_test.dart` - Remove provider overrides
- [ ] Add real TimeService tests to `time_service_test.dart`

### Phase 3 Tasks (Week 3)
- [ ] Create integration tests for booking flow
- [ ] Create integration tests for provider interactions
- [ ] Add database constraint testing
- [ ] Test real-time provider behavior with real data

### Phase 4 Tasks (Week 4)
- [ ] Review all remaining provider overrides
- [ ] Establish provider testing guidelines
- [ ] Update test documentation
- [ ] Final validation and cleanup

---

**Approval Required**: This plan must be reviewed and approved before proceeding with any test modifications to ensure alignment with established testing philosophy and project goals.
