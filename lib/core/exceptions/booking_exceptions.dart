// lib/core/exceptions/booking_exceptions.dart

/// Exception thrown when booking cancellation fails or is not allowed
class BookingCancellationException implements Exception {
  final String message;
  final String? code;
  final Exception? originalException;

  const BookingCancellationException(
    this.message, {
    this.code,
    this.originalException,
  });

  @override
  String toString() {
    return 'BookingCancellationException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// Exception thrown when booking view deletion is not allowed
class BookingViewDeletionException implements Exception {
  final String message;
  final String? code;

  const BookingViewDeletionException(this.message, {this.code});

  @override
  String toString() {
    return 'BookingViewDeletionException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}
