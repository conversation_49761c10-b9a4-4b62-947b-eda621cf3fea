/// Central location for all route constants in the Skillz app.
///
/// This file provides a single source of truth for all route paths and names,
/// preventing typos and making route management more maintainable.
library;

/// Main route constants for the Skillz application.
///
/// This file provides a single source of truth for all route paths and names,
/// preventing typos and making route management more maintainable.
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // ============================================================================
  // ROUTE PATHS - Used with context.go() and context.push()
  // ============================================================================

  /// Home page route path
  static const String homePath = '/';

  /// Availability screen route path
  static const String availabilityPath = '/availability';

  /// My bookings screen route path
  static const String myBookingsPath = '/my-bookings';

  /// Booking confirmation screen route path
  static const String bookingConfirmationPath = '/booking-confirmation';

  /// Login page route path
  static const String loginPath = '/login';

  /// Sign up page route path
  static const String signupPath = '/signup';

  // ============================================================================
  // ROUTE NAMES - Used with context.goNamed() and context.pushNamed()
  // ============================================================================

  /// Home page route name
  static const String homeName = 'home';

  /// Availability screen route name
  static const String availabilityName = 'availability';

  /// My bookings screen route name
  static const String myBookingsName = 'my-bookings';

  /// Booking confirmation screen route name
  static const String bookingConfirmationName = 'booking-confirmation';

  /// Login page route name
  static const String loginName = 'login';

  /// Sign up page route name
  static const String signupName = 'signup';

  // ============================================================================
  // CONVENIENCE GETTERS - For backwards compatibility
  // ============================================================================

  /// All route paths as a list (useful for validation)
  static List<String> get allPaths => [
    homePath,
    availabilityPath,
    myBookingsPath,
    bookingConfirmationPath,
    loginPath,
    signupPath,
  ];

  /// All route names as a list (useful for validation)
  static List<String> get allNames => [
    homeName,
    availabilityName,
    myBookingsName,
    bookingConfirmationName,
    loginName,
    signupName,
  ];
}

/// Authentication related route constants
class AuthRoutes {
  AuthRoutes._();

  static const String loginPath = AppRoutes.loginPath;
  static const String signupPath = AppRoutes.signupPath;
  static const String loginName = AppRoutes.loginName;
  static const String signupName = AppRoutes.signupName;
}

/// Booking related route constants
class BookingRoutes {
  BookingRoutes._();

  static const String myBookingsPath = AppRoutes.myBookingsPath;
  static const String bookingConfirmationPath =
      AppRoutes.bookingConfirmationPath;
  static const String myBookingsName = AppRoutes.myBookingsName;
  static const String bookingConfirmationName =
      AppRoutes.bookingConfirmationName;
}

/// Availability related route constants
class AvailabilityRoutes {
  AvailabilityRoutes._();

  static const String availabilityPath = AppRoutes.availabilityPath;
  static const String availabilityName = AppRoutes.availabilityName;
}
