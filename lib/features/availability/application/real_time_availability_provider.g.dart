// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'real_time_availability_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$databaseSlotProviderHash() =>
    r'69e165d7b5edb308a04a6c86eee0d913bd31c5e4';

/// Database slot provider instance
///
/// Copied from [databaseSlotProvider].
@ProviderFor(databaseSlotProvider)
final databaseSlotProviderProvider =
    AutoDisposeProvider<DatabaseSlotProvider>.internal(
      databaseSlotProvider,
      name: r'databaseSlotProviderProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$databaseSlotProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseSlotProviderRef = AutoDisposeProviderRef<DatabaseSlotProvider>;
String _$realTimeAvailabilityHash() =>
    r'81e885894703049df570996b51709223425703df';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Real-time availability stream using database-centric approach (ADR-007)
///
/// Copied from [realTimeAvailability].
@ProviderFor(realTimeAvailability)
const realTimeAvailabilityProvider = RealTimeAvailabilityFamily();

/// Real-time availability stream using database-centric approach (ADR-007)
///
/// Copied from [realTimeAvailability].
class RealTimeAvailabilityFamily
    extends Family<AsyncValue<List<TimeSlotInfo>>> {
  /// Real-time availability stream using database-centric approach (ADR-007)
  ///
  /// Copied from [realTimeAvailability].
  const RealTimeAvailabilityFamily();

  /// Real-time availability stream using database-centric approach (ADR-007)
  ///
  /// Copied from [realTimeAvailability].
  RealTimeAvailabilityProvider call(RealTimeAvailabilityParams params) {
    return RealTimeAvailabilityProvider(params);
  }

  @override
  RealTimeAvailabilityProvider getProviderOverride(
    covariant RealTimeAvailabilityProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'realTimeAvailabilityProvider';
}

/// Real-time availability stream using database-centric approach (ADR-007)
///
/// Copied from [realTimeAvailability].
class RealTimeAvailabilityProvider
    extends AutoDisposeStreamProvider<List<TimeSlotInfo>> {
  /// Real-time availability stream using database-centric approach (ADR-007)
  ///
  /// Copied from [realTimeAvailability].
  RealTimeAvailabilityProvider(RealTimeAvailabilityParams params)
    : this._internal(
        (ref) => realTimeAvailability(ref as RealTimeAvailabilityRef, params),
        from: realTimeAvailabilityProvider,
        name: r'realTimeAvailabilityProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$realTimeAvailabilityHash,
        dependencies: RealTimeAvailabilityFamily._dependencies,
        allTransitiveDependencies:
            RealTimeAvailabilityFamily._allTransitiveDependencies,
        params: params,
      );

  RealTimeAvailabilityProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final RealTimeAvailabilityParams params;

  @override
  Override overrideWith(
    Stream<List<TimeSlotInfo>> Function(RealTimeAvailabilityRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RealTimeAvailabilityProvider._internal(
        (ref) => create(ref as RealTimeAvailabilityRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<TimeSlotInfo>> createElement() {
    return _RealTimeAvailabilityProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RealTimeAvailabilityProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RealTimeAvailabilityRef
    on AutoDisposeStreamProviderRef<List<TimeSlotInfo>> {
  /// The parameter `params` of this provider.
  RealTimeAvailabilityParams get params;
}

class _RealTimeAvailabilityProviderElement
    extends AutoDisposeStreamProviderElement<List<TimeSlotInfo>>
    with RealTimeAvailabilityRef {
  _RealTimeAvailabilityProviderElement(super.provider);

  @override
  RealTimeAvailabilityParams get params =>
      (origin as RealTimeAvailabilityProvider).params;
}

String _$slotMarkingStateHash() => r'76b3a2455811a3f9edf2d2c0cad4ae89ed4a11ab';

/// Simple slot marking state for immediate UI feedback
///
/// Copied from [SlotMarkingState].
@ProviderFor(SlotMarkingState)
final slotMarkingStateProvider =
    AutoDisposeNotifierProvider<SlotMarkingState, Map<String, bool>>.internal(
      SlotMarkingState.new,
      name: r'slotMarkingStateProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$slotMarkingStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SlotMarkingState = AutoDisposeNotifier<Map<String, bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
