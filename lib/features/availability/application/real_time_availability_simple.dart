// Real-time availability provider implementing ADR-007 database-centric approach
// Simplified version that uses database streaming instead of polling

import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/core/utils/logger_service.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/features/availability/domain/real_time_availability_params.dart';
import 'package:skillz/features/availability/application/database_slot_provider.dart';
import 'package:skillz/features/auth/application/auth_service.dart';

part 'real_time_availability_simple.g.dart';

final logger = AppLogger();

/// Database slot provider for simple real-time availability
@riverpod
DatabaseSlotProvider simpleDatabaseSlotProvider(Ref ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return DatabaseSlotProvider(supabaseClient: supabase);
}

/// ADR-007 compliant real-time availability using database streaming
@riverpod
Stream<List<TimeSlotInfo>> simpleRealTimeAvailability(
  Ref ref,
  RealTimeAvailabilityParams params,
) async* {
  logger.d(
    '🚀 ADR-007 Simple: Starting database streaming for date: ${params.date}, pitch: ${params.pitchId}',
  );

  try {
    final provider = ref.read(simpleDatabaseSlotProviderProvider);

    // Use database provider's real-time streaming - no more polling!
    await for (final slots in provider.watchSlots(
      params.pitchId,
      params.date,
    )) {
      logger.d('📊 Simple stream update: ${slots.length} slots received');
      yield slots;
    }
  } catch (e) {
    logger.e('❌ Error in simple database streaming', e);

    // Fallback to direct fetch
    try {
      final provider = ref.read(simpleDatabaseSlotProviderProvider);
      final fallbackSlots = await provider.fetchSlots(
        params.pitchId,
        params.date,
      );
      yield fallbackSlots;
    } catch (fallbackError) {
      logger.e('❌ Simple fallback also failed', fallbackError);
      yield <TimeSlotInfo>[];
    }
  }
}

/// Simple slot marking state for immediate UI feedback
@riverpod
class SimpleSlotMarkingState extends _$SimpleSlotMarkingState {
  @override
  Map<String, bool> build() {
    return {}; // Maps slot key to optimistic booking state
  }

  void markSlotAsOptimisticallyBooked(DateTime startTime, DateTime endTime) {
    final slotKey =
        '${startTime.toIso8601String()}_${endTime.toIso8601String()}';
    state = {...state, slotKey: true};
    logger.d('Marked slot as optimistically booked: $slotKey');
  }

  void clearOptimisticBooking(DateTime startTime, DateTime endTime) {
    final slotKey =
        '${startTime.toIso8601String()}_${endTime.toIso8601String()}';
    final newState = Map<String, bool>.from(state);
    newState.remove(slotKey);
    state = newState;
    logger.d('Cleared optimistic booking: $slotKey');
  }

  void clearAllOptimisticBookings() {
    state = {};
    logger.d('Cleared all optimistic bookings');
  }
}

/// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
@riverpod
Future<List<TimeSlotInfo>> enhancedAvailability(
  Ref ref,
  RealTimeAvailabilityParams params,
) async {
  try {
    // Use database provider instead of legacy availability service
    final provider = ref.read(simpleDatabaseSlotProviderProvider);
    final slots = await provider.fetchSlots(params.pitchId, params.date);

    // Watch optimistic state
    final optimisticState = ref.read(simpleSlotMarkingStateProvider);
    final now = DateTime.now();

    // Apply time-based and optimistic updates
    final enhancedSlots =
        slots.map((slot) {
          final slotKey =
              '${slot.startTime.toIso8601String()}_${slot.endTime.toIso8601String()}';
          final isOptimisticallyBooked = optimisticState[slotKey] ?? false;

          // Mark past slots as unavailable
          if (slot.startTime.isBefore(now)) {
            return slot.copyWith(
              isBooked: true,
              bookingId: 'past_slot', // Special identifier for past slots
            );
          }

          // Apply optimistic booking state
          if (isOptimisticallyBooked && !slot.isBooked) {
            return slot.copyWith(
              isBooked: true,
              bookingId:
                  'optimistic', // Special identifier for optimistic bookings
            );
          }

          return slot;
        }).toList();

    return enhancedSlots;
  } catch (e, stackTrace) {
    logger.e('❌ Error in enhanced availability (ADR-007)', e, stackTrace);
    return <TimeSlotInfo>[];
  }
}
