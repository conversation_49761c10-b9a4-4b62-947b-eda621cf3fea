// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'real_time_availability_simple.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$simpleDatabaseSlotProviderHash() =>
    r'1d94a68efd356fbd8141bb50f94d19d698b97616';

/// Database slot provider for simple real-time availability
///
/// Copied from [simpleDatabaseSlotProvider].
@ProviderFor(simpleDatabaseSlotProvider)
final simpleDatabaseSlotProviderProvider =
    AutoDisposeProvider<DatabaseSlotProvider>.internal(
      simpleDatabaseSlotProvider,
      name: r'simpleDatabaseSlotProviderProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$simpleDatabaseSlotProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SimpleDatabaseSlotProviderRef =
    AutoDisposeProviderRef<DatabaseSlotProvider>;
String _$simpleRealTimeAvailabilityHash() =>
    r'7a3afbe6ce7b71c096b38ecf3932df61fa097cb5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// ADR-007 compliant real-time availability using database streaming
///
/// Copied from [simpleRealTimeAvailability].
@ProviderFor(simpleRealTimeAvailability)
const simpleRealTimeAvailabilityProvider = SimpleRealTimeAvailabilityFamily();

/// ADR-007 compliant real-time availability using database streaming
///
/// Copied from [simpleRealTimeAvailability].
class SimpleRealTimeAvailabilityFamily
    extends Family<AsyncValue<List<TimeSlotInfo>>> {
  /// ADR-007 compliant real-time availability using database streaming
  ///
  /// Copied from [simpleRealTimeAvailability].
  const SimpleRealTimeAvailabilityFamily();

  /// ADR-007 compliant real-time availability using database streaming
  ///
  /// Copied from [simpleRealTimeAvailability].
  SimpleRealTimeAvailabilityProvider call(RealTimeAvailabilityParams params) {
    return SimpleRealTimeAvailabilityProvider(params);
  }

  @override
  SimpleRealTimeAvailabilityProvider getProviderOverride(
    covariant SimpleRealTimeAvailabilityProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'simpleRealTimeAvailabilityProvider';
}

/// ADR-007 compliant real-time availability using database streaming
///
/// Copied from [simpleRealTimeAvailability].
class SimpleRealTimeAvailabilityProvider
    extends AutoDisposeStreamProvider<List<TimeSlotInfo>> {
  /// ADR-007 compliant real-time availability using database streaming
  ///
  /// Copied from [simpleRealTimeAvailability].
  SimpleRealTimeAvailabilityProvider(RealTimeAvailabilityParams params)
    : this._internal(
        (ref) => simpleRealTimeAvailability(
          ref as SimpleRealTimeAvailabilityRef,
          params,
        ),
        from: simpleRealTimeAvailabilityProvider,
        name: r'simpleRealTimeAvailabilityProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$simpleRealTimeAvailabilityHash,
        dependencies: SimpleRealTimeAvailabilityFamily._dependencies,
        allTransitiveDependencies:
            SimpleRealTimeAvailabilityFamily._allTransitiveDependencies,
        params: params,
      );

  SimpleRealTimeAvailabilityProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final RealTimeAvailabilityParams params;

  @override
  Override overrideWith(
    Stream<List<TimeSlotInfo>> Function(SimpleRealTimeAvailabilityRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SimpleRealTimeAvailabilityProvider._internal(
        (ref) => create(ref as SimpleRealTimeAvailabilityRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<TimeSlotInfo>> createElement() {
    return _SimpleRealTimeAvailabilityProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SimpleRealTimeAvailabilityProvider &&
        other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SimpleRealTimeAvailabilityRef
    on AutoDisposeStreamProviderRef<List<TimeSlotInfo>> {
  /// The parameter `params` of this provider.
  RealTimeAvailabilityParams get params;
}

class _SimpleRealTimeAvailabilityProviderElement
    extends AutoDisposeStreamProviderElement<List<TimeSlotInfo>>
    with SimpleRealTimeAvailabilityRef {
  _SimpleRealTimeAvailabilityProviderElement(super.provider);

  @override
  RealTimeAvailabilityParams get params =>
      (origin as SimpleRealTimeAvailabilityProvider).params;
}

String _$enhancedAvailabilityHash() =>
    r'f07258a380b6e6c96084cc2c913289416706f22f';

/// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
///
/// Copied from [enhancedAvailability].
@ProviderFor(enhancedAvailability)
const enhancedAvailabilityProvider = EnhancedAvailabilityFamily();

/// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
///
/// Copied from [enhancedAvailability].
class EnhancedAvailabilityFamily
    extends Family<AsyncValue<List<TimeSlotInfo>>> {
  /// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
  ///
  /// Copied from [enhancedAvailability].
  const EnhancedAvailabilityFamily();

  /// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
  ///
  /// Copied from [enhancedAvailability].
  EnhancedAvailabilityProvider call(RealTimeAvailabilityParams params) {
    return EnhancedAvailabilityProvider(params);
  }

  @override
  EnhancedAvailabilityProvider getProviderOverride(
    covariant EnhancedAvailabilityProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'enhancedAvailabilityProvider';
}

/// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
///
/// Copied from [enhancedAvailability].
class EnhancedAvailabilityProvider
    extends AutoDisposeFutureProvider<List<TimeSlotInfo>> {
  /// Enhanced availability provider with optimistic updates and time sensitivity (ADR-007)
  ///
  /// Copied from [enhancedAvailability].
  EnhancedAvailabilityProvider(RealTimeAvailabilityParams params)
    : this._internal(
        (ref) => enhancedAvailability(ref as EnhancedAvailabilityRef, params),
        from: enhancedAvailabilityProvider,
        name: r'enhancedAvailabilityProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$enhancedAvailabilityHash,
        dependencies: EnhancedAvailabilityFamily._dependencies,
        allTransitiveDependencies:
            EnhancedAvailabilityFamily._allTransitiveDependencies,
        params: params,
      );

  EnhancedAvailabilityProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final RealTimeAvailabilityParams params;

  @override
  Override overrideWith(
    FutureOr<List<TimeSlotInfo>> Function(EnhancedAvailabilityRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EnhancedAvailabilityProvider._internal(
        (ref) => create(ref as EnhancedAvailabilityRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<TimeSlotInfo>> createElement() {
    return _EnhancedAvailabilityProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EnhancedAvailabilityProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EnhancedAvailabilityRef
    on AutoDisposeFutureProviderRef<List<TimeSlotInfo>> {
  /// The parameter `params` of this provider.
  RealTimeAvailabilityParams get params;
}

class _EnhancedAvailabilityProviderElement
    extends AutoDisposeFutureProviderElement<List<TimeSlotInfo>>
    with EnhancedAvailabilityRef {
  _EnhancedAvailabilityProviderElement(super.provider);

  @override
  RealTimeAvailabilityParams get params =>
      (origin as EnhancedAvailabilityProvider).params;
}

String _$simpleSlotMarkingStateHash() =>
    r'435f6c0862cfde741e78dd9e25a6da7dc0680316';

/// Simple slot marking state for immediate UI feedback
///
/// Copied from [SimpleSlotMarkingState].
@ProviderFor(SimpleSlotMarkingState)
final simpleSlotMarkingStateProvider = AutoDisposeNotifierProvider<
  SimpleSlotMarkingState,
  Map<String, bool>
>.internal(
  SimpleSlotMarkingState.new,
  name: r'simpleSlotMarkingStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$simpleSlotMarkingStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SimpleSlotMarkingState = AutoDisposeNotifier<Map<String, bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
