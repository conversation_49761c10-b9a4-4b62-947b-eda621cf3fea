class PitchSettings {
  final int id;
  final String name; // Added name field
  final String
  openTime; // Store as String, parse to TimeOfDay or DateTime in UI/logic if needed
  final String
  closeTime; // Store as String, parse to TimeOfDay or DateTime in UI/logic if needed
  final int slotDurationMinutes;
  final int cancellationWindowHours;
  final double pricePerHour; // NEW: Hourly rate for pricing calculations
  final int maxBookingsPerUser; // NEW: Dynamic booking limit per user
  final bool
  isEnabled; // NEW: Controls whether the pitch is available for booking
  final DateTime createdAt;
  final DateTime updatedAt;

  PitchSettings({
    required this.id,
    required this.name, // Added name to constructor
    required this.openTime,
    required this.closeTime,
    required this.slotDurationMinutes,
    required this.cancellationWindowHours,
    required this.pricePerHour, // NEW: Required pricing field
    required this.maxBookingsPerUser, // NEW: Required limit field
    required this.isEnabled, // NEW: Required enabled state field
    required this.createdAt,
    required this.updatedAt,
  });

  // Business logic methods for pricing calculations
  double calculateSlotPrice() {
    final durationHours = slotDurationMinutes / 60.0;
    return pricePerHour * durationHours;
  }

  // Helper method for UI display
  String get formattedSlotPrice {
    final price = calculateSlotPrice();
    return 'MWK ${price.toStringAsFixed(0)}'; // Assuming Malawi Kwacha
  }

  factory PitchSettings.fromJson(Map<String, dynamic> json) {
    return PitchSettings(
      id: json['id'] as int,
      name: json['pitch_name'] as String, // Changed from json['name']
      openTime: json['open_time'] as String,
      closeTime: json['close_time'] as String,
      slotDurationMinutes: json['slot_duration_minutes'] as int,
      cancellationWindowHours: json['cancellation_window_hours'] as int,
      pricePerHour:
          (json['price_per_hour'] as num).toDouble(), // NEW: Parse pricing
      maxBookingsPerUser:
          json['max_bookings_per_user'] as int, // NEW: Parse limit
      isEnabled: json['is_enabled'] as bool, // NEW: Parse enabled state
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pitch_name': name, // Changed from 'name' to 'pitch_name' for consistency
      'open_time': openTime,
      'close_time': closeTime,
      'slot_duration_minutes': slotDurationMinutes,
      'cancellation_window_hours': cancellationWindowHours,
      'price_per_hour': pricePerHour, // NEW: Include pricing in serialization
      'max_bookings_per_user':
          maxBookingsPerUser, // NEW: Include limit in serialization
      'is_enabled': isEnabled, // NEW: Include enabled state in serialization
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
