/// Parameters for real-time availability provider
///
/// This class encapsulates the required parameters for querying
/// real-time availability data for a specific pitch on a given date.
class RealTimeAvailabilityParams {
  /// The date for which to query availability
  final DateTime date;

  /// The ID of the pitch to query
  final int pitchId;

  /// Creates a new instance of [RealTimeAvailabilityParams]
  const RealTimeAvailabilityParams({required this.date, required this.pitchId});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RealTimeAvailabilityParams &&
          runtimeType == other.runtimeType &&
          date == other.date &&
          pitchId == other.pitchId;

  @override
  int get hashCode => date.hashCode ^ pitchId.hashCode;

  @override
  String toString() =>
      'RealTimeAvailabilityParams(date: $date, pitchId: $pitchId)';
}
