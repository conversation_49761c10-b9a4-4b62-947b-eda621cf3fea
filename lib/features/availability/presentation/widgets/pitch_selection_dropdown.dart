import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart'; // Corrected import
import 'package:skillz/core/utils/logger_service.dart';

class PitchSelectionDropdown extends ConsumerWidget {
  final List<PitchSettings> pitches; // Corrected type to PitchSettings
  final int? selectedPitchId;

  const PitchSelectionDropdown({
    super.key,
    required this.pitches,
    required this.selectedPitchId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the default active pitch provider
    final defaultActivePitchAsync = ref.watch(defaultActivePitchProvider);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(selectedPitchIdProvider) == null && pitches.isNotEmpty) {
        // Use the default active pitch if available, otherwise fall back to first pitch
        defaultActivePitchAsync.whenData((defaultPitchId) {
          final pitchIdToSelect = defaultPitchId ?? pitches.first.id;
          logger.d(
            '[PitchSelectionDropdown] Auto-selecting default active pitch: $pitchIdToSelect',
          );
          ref.read(selectedPitchIdProvider.notifier).state = pitchIdToSelect;
        });
      }
    });

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: DropdownButtonFormField<int>(
        decoration: const InputDecoration(labelText: 'Select Pitch'),
        value: selectedPitchId,
        items:
            pitches.map((pitch) {
              return DropdownMenuItem<int>(
                value:
                    pitch
                        .id, // pitch.id is non-null based on PitchSettings model
                child: Text(
                  pitch.name, // pitch.name is non-null
                  style: const TextStyle(color: Colors.black),
                ),
              );
            }).toList(),
        onChanged: (value) {
          if (value != null) {
            logger.i('[PitchSelectionDropdown] Pitch selected: $value');
            ref.read(selectedPitchIdProvider.notifier).state = value;
          }
        },
        hint:
            selectedPitchId == null && pitches.isNotEmpty
                ? const Text("Select a pitch")
                : null,
        disabledHint: pitches.isEmpty ? const Text("No pitches loaded") : null,
      ),
    );
  }
}
