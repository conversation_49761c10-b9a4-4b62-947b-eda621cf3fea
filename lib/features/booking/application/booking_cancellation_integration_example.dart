// lib/features/booking/application/booking_cancellation_integration_example.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/booking/application/booking_cancellation_service.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/booking/domain/booking_cancellation_rules.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/core/services/time_service.dart';
import 'package:skillz/core/utils/logger_service.dart';

/// Example integration service showing how to use BookingCancellationService
/// with backend-fetched PitchSettings for dynamic cancellation windows
///
/// This demonstrates the complete flow:
/// 1. Fetch PitchSettings from backend (contains cancellation window from database)
/// 2. Apply business rules using the backend-configured window
/// 3. Cancel booking if eligible
class BookingCancellationIntegration {
  final Ref _ref;
  final logger = AppLogger();

  BookingCancellationIntegration(this._ref);

  /// Cancels a booking using backend-configured cancellation window
  ///
  /// This method demonstrates the proper integration pattern:
  /// 1. Fetch pitch settings from backend (dynamic cancellation window)
  /// 2. Use BookingCancellationService with the fetched settings
  /// 3. Handle errors and state updates
  Future<void> cancelBookingWithBackendSettings({
    required Booking booking,
    required void Function(String message) onSuccess,
    required void Function(String error) onError,
  }) async {
    try {
      logger.i(
        '[BookingCancellationIntegration] Starting cancellation for booking ${booking.id}',
      );

      // 1. Fetch PitchSettings from backend - this contains the cancellation window
      // configured by the admin and stored in the database
      logger.d(
        '[BookingCancellationIntegration] Fetching pitch settings for pitch ${booking.pitchId}',
      );
      final pitchSettings = await _ref.read(
        pitchSettingsProvider(booking.pitchId).future,
      );

      logger.d(
        '[BookingCancellationIntegration] Retrieved cancellation window: ${pitchSettings.cancellationWindowHours} hours',
      );

      // 2. Use the cancellation service with the backend-fetched settings
      // The service will apply BookingCancellationRules with the dynamic window
      final cancellationService = _ref.read(
        bookingCancellationServiceProvider.notifier,
      );
      await cancellationService.cancelBooking(booking, pitchSettings);

      // 3. Check final state and notify UI
      final finalState = _ref.read(bookingCancellationServiceProvider);
      if (finalState.isSuccess) {
        logger.i(
          '[BookingCancellationIntegration] Cancellation successful for booking ${booking.id}',
        );
        onSuccess(finalState.message ?? 'Booking cancelled successfully');
      } else if (finalState.isError) {
        logger.w(
          '[BookingCancellationIntegration] Cancellation failed for booking ${booking.id}: ${finalState.message}',
        );
        onError(finalState.message ?? 'Failed to cancel booking');
      }
    } catch (e, stackTrace) {
      logger.e(
        '[BookingCancellationIntegration] Error during cancellation process',
        e,
        stackTrace,
      );
      onError('An unexpected error occurred while cancelling the booking');
    }
  }

  /// Deletes a booking from local view with backend validation
  ///
  /// This method demonstrates local deletion integration:
  /// 1. No need to fetch PitchSettings (deletion rules are time-based only)
  /// 2. Use BookingCancellationService for consistent error handling
  /// 3. Handle state updates properly
  Future<void> deleteBookingFromViewWithValidation({
    required Booking booking,
    required void Function(String message) onSuccess,
    required void Function(String error) onError,
  }) async {
    try {
      logger.i(
        '[BookingCancellationIntegration] Starting local deletion for booking ${booking.id}',
      );

      // Use the cancellation service for local deletion
      // This ensures consistent business rule validation and error handling
      final cancellationService = _ref.read(
        bookingCancellationServiceProvider.notifier,
      );
      await cancellationService.deleteBookingFromView(booking);

      // Check final state and notify UI
      final finalState = _ref.read(bookingCancellationServiceProvider);
      if (finalState.isSuccess) {
        logger.i(
          '[BookingCancellationIntegration] Local deletion successful for booking ${booking.id}',
        );
        onSuccess(finalState.message ?? 'Booking removed from your view');
      } else if (finalState.isError) {
        logger.w(
          '[BookingCancellationIntegration] Local deletion failed for booking ${booking.id}: ${finalState.message}',
        );
        onError(finalState.message ?? 'Failed to remove booking from view');
      }
    } catch (e, stackTrace) {
      logger.e(
        '[BookingCancellationIntegration] Error during local deletion process',
        e,
        stackTrace,
      );
      onError(
        'An unexpected error occurred while removing the booking from your view',
      );
    }
  }

  /// Gets cancellation information for a booking using backend settings
  ///
  /// This method can be used to display cancellation deadlines and remaining time
  /// in the UI, using the actual backend-configured cancellation window
  Future<Map<String, dynamic>> getCancellationInfo(Booking booking) async {
    try {
      logger.d(
        '[BookingCancellationIntegration] Getting cancellation info for booking ${booking.id}',
      );

      // Fetch backend settings
      final pitchSettings = await _ref.read(
        pitchSettingsProvider(booking.pitchId).future,
      );
      final timeService = _ref.read(timeServiceProvider);
      final currentTime = timeService.now();

      // Calculate using domain rules with backend settings
      final canCancel = BookingCancellationRules.canCancelBooking(
        booking,
        pitchSettings,
        currentTime,
      );

      final deadline = BookingCancellationRules.getCancellationDeadline(
        booking,
        pitchSettings,
      );

      final timeRemaining =
          BookingCancellationRules.getCancellationTimeRemaining(
            booking,
            pitchSettings,
            currentTime,
          );

      return {
        'canCancel': canCancel,
        'cancellationDeadline': deadline,
        'timeRemaining': timeRemaining,
        'cancellationWindowHours':
            pitchSettings.cancellationWindowHours, // Backend value
        'pitchName': pitchSettings.name,
      };
    } catch (e, stackTrace) {
      logger.e(
        '[BookingCancellationIntegration] Error getting cancellation info',
        e,
        stackTrace,
      );
      rethrow;
    }
  }
}

/// Provider for the integration service
final bookingCancellationIntegrationProvider =
    Provider<BookingCancellationIntegration>((ref) {
      return BookingCancellationIntegration(ref);
    });
