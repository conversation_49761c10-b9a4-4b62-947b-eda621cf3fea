// lib/features/booking/application/booking_cancellation_service.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/core/services/time_service.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/features/booking/domain/booking_cancellation_rules.dart';
import 'package:skillz/core/exceptions/booking_exceptions.dart';
import 'package:skillz/core/utils/logger_service.dart';

/// State for booking cancellation operations
enum BookingCancellationStatus { idle, loading, success, error }

/// State class for booking cancellation operations
class BookingCancellationState {
  final BookingCancellationStatus status;
  final String? message;
  final Exception? exception;

  const BookingCancellationState({
    required this.status,
    this.message,
    this.exception,
  });

  /// Convenience getters for state checking
  bool get isIdle => status == BookingCancellationStatus.idle;
  bool get isLoading => status == BookingCancellationStatus.loading;
  bool get isSuccess => status == BookingCancellationStatus.success;
  bool get isError => status == BookingCancellationStatus.error;

  /// Copy with method for state updates
  BookingCancellationState copyWith({
    BookingCancellationStatus? status,
    String? message,
    Exception? exception,
  }) {
    return BookingCancellationState(
      status: status ?? this.status,
      message: message ?? this.message,
      exception: exception ?? this.exception,
    );
  }

  @override
  String toString() {
    return 'BookingCancellationState(status: $status, message: $message, exception: $exception)';
  }
}

/// Service for handling booking cancellation and local deletion operations
///
/// This service coordinates between the domain rules, repository, and UI state.
/// It ensures all cancellation business logic is applied and provides proper
/// error handling and state management.
class BookingCancellationService
    extends StateNotifier<BookingCancellationState> {
  final BookingRepository _repository;
  final TimeService _timeService;
  final logger = AppLogger();

  BookingCancellationService({
    required BookingRepository repository,
    required TimeService timeService,
  }) : _repository = repository,
       _timeService = timeService,
       super(
         const BookingCancellationState(status: BookingCancellationStatus.idle),
       );

  /// Cancels a booking if it meets the business rules criteria
  ///
  /// This method:
  /// 1. Validates the booking can be cancelled using domain rules
  /// 2. Calls the repository to update the booking status
  /// 3. Updates the service state to reflect the operation result
  ///
  /// Throws [BookingCancellationException] if the booking cannot be cancelled
  /// based on business rules (time window, status, etc.)
  Future<void> cancelBooking(
    Booking booking,
    PitchSettings pitchSettings,
  ) async {
    // Prevent multiple simultaneous operations
    if (state.isLoading) {
      logger.w(
        '[BookingCancellationService] Cancellation already in progress, ignoring request',
      );
      return;
    }

    try {
      state = state.copyWith(status: BookingCancellationStatus.loading);

      final currentTime = _timeService.now();

      // Apply business rules validation
      if (!BookingCancellationRules.canCancelBooking(
        booking,
        pitchSettings,
        currentTime,
      )) {
        final timeRemaining =
            BookingCancellationRules.getCancellationTimeRemaining(
              booking,
              pitchSettings,
              currentTime,
            );

        String errorMessage;
        if (timeRemaining == Duration.zero) {
          errorMessage =
              'Cannot cancel booking: The cancellation window has expired. '
              'Bookings must be cancelled at least ${pitchSettings.cancellationWindowHours} hours in advance.';
        } else if (!BookingCancellationRules.isValidStatusForCancellation(
          booking.status,
        )) {
          errorMessage =
              'Cannot cancel booking: Booking status (${booking.status.name}) does not allow cancellation.';
        } else {
          errorMessage =
              'Cannot cancel booking: Booking has already started or passed.';
        }

        throw BookingCancellationException(errorMessage);
      }

      // Call repository to cancel the booking
      logger.d('[BookingCancellationService] Cancelling booking ${booking.id}');
      await _repository.cancelBooking(booking.id);

      state = state.copyWith(
        status: BookingCancellationStatus.success,
        message:
            'Booking cancelled successfully. You will not be charged for this slot.',
        exception: null,
      );

      logger.i(
        '[BookingCancellationService] Successfully cancelled booking ${booking.id}',
      );
    } on BookingCancellationException {
      // Re-throw business rule violations as-is
      state = state.copyWith(status: BookingCancellationStatus.idle);
      rethrow;
    } catch (e, stackTrace) {
      logger.e(
        '[BookingCancellationService] Error cancelling booking ${booking.id}',
        e,
        stackTrace,
      );

      state = state.copyWith(
        status: BookingCancellationStatus.error,
        message:
            'Failed to cancel booking. Please try again or contact support if the problem persists.',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Deletes a booking from the user's local view (frontend only)
  ///
  /// This is used for past bookings to help users clean up their booking history.
  /// The booking is not actually deleted from the database, just hidden from the user's view.
  ///
  /// Throws [BookingCancellationException] if the booking is not eligible for local deletion
  /// (i.e., if it's not a past booking that has completely ended)
  Future<void> deleteBookingFromView(Booking booking) async {
    // Prevent multiple simultaneous operations
    if (state.isLoading) {
      logger.w(
        '[BookingCancellationService] Delete operation already in progress, ignoring request',
      );
      return;
    }

    try {
      state = state.copyWith(status: BookingCancellationStatus.loading);

      final currentTime = _timeService.now();

      // Apply business rules validation for local deletion
      if (!BookingCancellationRules.canDeleteBookingFromView(
        booking,
        currentTime,
      )) {
        throw BookingCancellationException(
          'Cannot remove booking from view: Only past bookings that have completely ended can be removed from your booking history.',
        );
      }

      // Call repository to hide the booking from user's view
      logger.d(
        '[BookingCancellationService] Deleting booking ${booking.id} from user view',
      );
      await _repository.deleteBookingFromView(booking.id);

      state = state.copyWith(
        status: BookingCancellationStatus.success,
        message:
            'Booking removed from your view. This does not affect your booking history in our records.',
        exception: null,
      );

      logger.i(
        '[BookingCancellationService] Successfully removed booking ${booking.id} from user view',
      );
    } on BookingCancellationException {
      // Re-throw business rule violations as-is
      state = state.copyWith(status: BookingCancellationStatus.idle);
      rethrow;
    } catch (e, stackTrace) {
      logger.e(
        '[BookingCancellationService] Error deleting booking ${booking.id} from view',
        e,
        stackTrace,
      );

      state = state.copyWith(
        status: BookingCancellationStatus.error,
        message:
            'Failed to remove booking from view. Please try again or contact support if the problem persists.',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Resets the service state to idle
  ///
  /// This is useful for clearing error states or preparing for new operations
  void resetState() {
    state = const BookingCancellationState(
      status: BookingCancellationStatus.idle,
    );
    logger.d('[BookingCancellationService] State reset to idle');
  }
}

/// Riverpod provider for the booking cancellation service
final bookingCancellationServiceProvider =
    StateNotifierProvider<BookingCancellationService, BookingCancellationState>(
      (ref) {
        final repository = ref.watch(bookingRepositoryProvider);
        final timeService = ref.watch(timeServiceProvider);

        return BookingCancellationService(
          repository: repository,
          timeService: timeService,
        );
      },
    );
