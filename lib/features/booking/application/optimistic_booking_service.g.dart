// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'optimistic_booking_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$optimisticBookingServiceHash() =>
    r'ffcac209865195b060aa430834ee5ab282882b36';

/// Optimistic booking service with race condition handling
///
/// Copied from [OptimisticBookingService].
@ProviderFor(OptimisticBookingService)
final optimisticBookingServiceProvider = AutoDisposeNotifierProvider<
  OptimisticBookingService,
  OptimisticBookingState
>.internal(
  OptimisticBookingService.new,
  name: r'optimisticBookingServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$optimisticBookingServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OptimisticBookingService =
    AutoDisposeNotifier<OptimisticBookingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
