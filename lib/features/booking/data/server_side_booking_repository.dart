// Server-side booking repository that leverages the new database views and functions
// This replaces client-side filtering logic with server-side state management

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:skillz/core/utils/logger_service.dart';

final serverSideBookingRepositoryProvider =
    Provider<ServerSideBookingRepository>((ref) {
      return ServerSideBookingRepository(ref.watch(supabaseClientProvider));
    });

/// Repository that uses server-side views and functions for booking management
/// This eliminates client-side time filtering and ensures consistent state transitions
class ServerSideBookingRepository {
  final SupabaseClient _client;
  final logger = AppLogger();

  ServerSideBookingRepository(this._client);

  /// Fetch user's active bookings using the server-side active_bookings view
  /// This replaces client-side filtering for booking limits and active counts
  Future<List<Booking>> fetchActiveBookings() async {
    try {
      logger.d(
        '[ServerSideBookingRepository] Fetching active bookings from server-side view',
      );

      final response = await _client
          .from('active_bookings')
          .select(
            'id, user_id, pitch_id, slot_start_time, slot_end_time, status, created_at, updated_at',
          )
          .order('slot_start_time', ascending: true);

      logger.d(
        '[ServerSideBookingRepository] Received ${response.length} active bookings',
      );

      final bookings = <Booking>[];
      for (final item in response) {
        try {
          // Validate required fields before processing
          if (item['id'] == null || item['pitch_id'] == null) {
            logger.w(
              '[ServerSideBookingRepository] Skipping active booking with null id or pitch_id: $item',
            );
            continue;
          }

          final booking = Booking.fromJson(item);
          bookings.add(booking);
        } catch (e, stackTrace) {
          logger.e(
            '[ServerSideBookingRepository] Error processing active booking item: $item',
            e,
            stackTrace,
          );
          // Continue processing other bookings
          continue;
        }
      }

      return bookings;
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error fetching active bookings',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Fetch user's bookings categorized by time status
  /// Uses the server-side user_booking_categories view for consistent categorization
  Future<Map<String, List<Booking>>> fetchCategorizedBookings() async {
    try {
      logger.d(
        '[ServerSideBookingRepository] Fetching categorized bookings from server-side view',
      );

      final response = await _client
          .from('user_booking_categories')
          .select(
            'id, user_id, pitch_id, slot_start_time, slot_end_time, status, created_at, updated_at, booking_category',
          )
          .order('slot_start_time', ascending: false); // Most recent first

      logger.d(
        '[ServerSideBookingRepository] Received ${response.length} categorized bookings',
      );

      // Group by server-determined category first, then create bookings
      final categorized = <String, List<Booking>>{
        'upcoming': [],
        'current': [],
        'past': [],
      };

      // Log detailed booking information for investigation
      final categoryStats = <String, int>{};

      for (final item in response) {
        try {
          // Extract booking category first and log details
          final category = item['booking_category'] as String?;
          final slotStart = item['slot_start_time'] as String?;
          final slotEnd = item['slot_end_time'] as String?;
          final status = item['status'] as String?;
          final bookingId = item['id'];

          logger.d(
            '[ServerSideBookingRepository] Processing booking $bookingId: '
            'category="$category", status="$status", '
            'start="$slotStart", end="$slotEnd"',
          );

          // Track category statistics
          categoryStats[category ?? 'null'] =
              (categoryStats[category ?? 'null'] ?? 0) + 1;

          // Create a clean JSON object for Booking.fromJson (without booking_category)
          final bookingJson = Map<String, dynamic>.from(item);
          bookingJson.remove('booking_category'); // Remove extra field

          // Handle potential null values with proper defaults/validation
          if (bookingJson['id'] == null) {
            logger.w(
              '[ServerSideBookingRepository] Skipping booking with null id: $item',
            );
            continue;
          }

          if (bookingJson['pitch_id'] == null) {
            logger.w(
              '[ServerSideBookingRepository] Skipping booking with null pitch_id: $item',
            );
            continue;
          }

          final booking = Booking.fromJson(bookingJson);

          // The server-side view should only return 'upcoming', 'current', or 'past'
          String targetCategory;
          switch (category) {
            case 'upcoming':
              targetCategory = 'upcoming';
              break;
            case 'current':
              targetCategory = 'current';
              break;
            case 'past':
              targetCategory = 'past';
              break;
            case 'active': // Legacy handling - should not happen with updated view
              logger.w(
                '[ServerSideBookingRepository] WARNING: Got legacy "active" category for booking $bookingId. This suggests the view is not updated.',
              );
              targetCategory = 'upcoming'; // Fallback to upcoming
              break;
            default:
              logger.w(
                '[ServerSideBookingRepository] Unexpected category: $category, adding to past',
              );
              targetCategory = 'past';
          }

          categorized[targetCategory]!.add(booking);
        } catch (e, stackTrace) {
          logger.e(
            '[ServerSideBookingRepository] Error processing booking item: $item',
            e,
            stackTrace,
          );
          // Continue processing other bookings instead of failing completely
          continue;
        }
      }

      // Log category statistics for debugging
      logger.d(
        '[ServerSideBookingRepository] Category breakdown from server: $categoryStats',
      );

      logger.d(
        '[ServerSideBookingRepository] Final categorized: ${categorized['upcoming']?.length} upcoming, ${categorized['current']?.length} current, ${categorized['past']?.length} past',
      );

      return categorized;
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error fetching categorized bookings',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get active booking count for limit enforcement using server-side counting
  /// Uses the new user_booking_stats table for O(1) lookup instead of counting
  Future<int> getActiveBookingCount() async {
    try {
      logger.d(
        '[ServerSideBookingRepository] Getting active booking count from server-side stats table',
      );

      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        logger.w('[ServerSideBookingRepository] User not authenticated');
        return 0;
      }

      // Default to pitch 1 for now - in future we can make this pitch-specific
      const pitchId = 1;

      logger.d(
        '[ServerSideBookingRepository] Calling get_user_active_booking_count_v2 for user: ${currentUser.id}, pitch: $pitchId',
      );

      final response = await _client.rpc(
        'get_user_active_booking_count_v2',
        params: {'p_user_id': currentUser.id, 'p_pitch_id': pitchId},
      );

      final count = response as int? ?? 0;
      logger.d(
        '[ServerSideBookingRepository] 📊 Server-side count from stats table: $count',
      );

      // Also log what the old counting method would return for comparison
      final fallbackCount = await _getActiveBookingCountFallback();
      logger.d(
        '[ServerSideBookingRepository] 📊 Fallback count (old method): $fallbackCount',
      );

      if (count != fallbackCount) {
        logger.w(
          '[ServerSideBookingRepository] ⚠️ COUNT MISMATCH: Server-side=$count, Fallback=$fallbackCount',
        );
      }

      return count;
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error getting active booking count from stats table',
        e,
        stackTrace,
      );
      // Fallback to old counting method if the new approach fails
      return _getActiveBookingCountFallback();
    }
  }

  /// Fallback method using the old counting approach (for backwards compatibility)
  Future<int> _getActiveBookingCountFallback() async {
    try {
      logger.d('[ServerSideBookingRepository] Using fallback counting method');
      final response = await _client.from('active_bookings').select('id');
      final count = response.length;
      logger.d(
        '[ServerSideBookingRepository] Fallback active booking count: $count',
      );
      return count;
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error in fallback booking count method',
        e,
        stackTrace,
      );
      return 0;
    }
  }

  /// Trigger server-side booking state update
  /// This calls the update_booking_states() function to transition past bookings
  Future<Map<String, dynamic>> updateBookingStates() async {
    try {
      logger.d(
        '[ServerSideBookingRepository] Triggering server-side booking state update',
      );

      final response = await _client.rpc('update_booking_states');

      logger.i(
        '[ServerSideBookingRepository] Booking state update result: $response',
      );

      return response as Map<String, dynamic>;
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error updating booking states',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Fetch all user bookings (legacy method for backward compatibility)
  Future<List<Booking>> fetchUserBookings() async {
    try {
      logger.d('[ServerSideBookingRepository] Fetching all user bookings');

      final response = await _client
          .from('bookings')
          .select()
          .order('slot_start_time', ascending: false);

      logger.d(
        '[ServerSideBookingRepository] Received ${response.length} total bookings',
      );

      return response.map((json) => Booking.fromJson(json)).toList();
    } catch (e, stackTrace) {
      logger.e(
        '[ServerSideBookingRepository] Error fetching user bookings',
        e,
        stackTrace,
      );
      rethrow;
    }
  }
}

/// Riverpod providers for server-side booking management

/// Provider for active bookings (replaces client-side filtering)
final activeBookingsProvider = FutureProvider<List<Booking>>((ref) async {
  final repository = ref.watch(serverSideBookingRepositoryProvider);
  return repository.fetchActiveBookings();
});

/// Provider for categorized bookings (for My Bookings screen)
final categorizedBookingsProvider = FutureProvider<Map<String, List<Booking>>>((
  ref,
) async {
  final repository = ref.watch(serverSideBookingRepositoryProvider);
  return repository.fetchCategorizedBookings();
});

/// Provider for active booking count (for booking limits)
final activeBookingCountProvider = FutureProvider<int>((ref) async {
  final repository = ref.watch(serverSideBookingRepositoryProvider);
  return repository.getActiveBookingCount();
});
