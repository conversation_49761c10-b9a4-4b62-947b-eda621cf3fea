// lib/features/booking/domain/booking_cancellation_rules.dart

import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';

/// Domain service for booking cancellation business rules
///
/// This class centralizes all cancellation logic including:
/// - Cancellation window validation (fetched from backend)
/// - Booking status validation
/// - Time-based eligibility checks
/// - Local view deletion rules for past bookings
class BookingCancellationRules {
  // Private constructor to prevent instantiation
  BookingCancellationRules._();

  /// Determines if a booking can be cancelled based on business rules
  ///
  /// A booking can be cancelled if:
  /// 1. It's in a cancellable status (confirmed, pending_payment)
  /// 2. The current time is before the cancellation deadline
  /// 3. The booking is not already in the past
  ///
  /// The cancellation window is fetched from the backend (PitchSettings)
  static bool canCancelBooking(
    Booking booking,
    PitchSettings pitchSettings,
    DateTime currentTime,
  ) {
    // Check if booking status allows cancellation
    if (!isValidStatusForCancellation(booking.status)) {
      return false;
    }

    // Cannot cancel past bookings
    if (booking.slotStartTime.isBefore(currentTime)) {
      return false;
    }

    // Check if within cancellation window
    final cancellationDeadline = getCancellationDeadline(
      booking,
      pitchSettings,
    );
    return currentTime.isBefore(cancellationDeadline);
  }

  /// Calculates the cancellation deadline for a booking
  ///
  /// The deadline is calculated by subtracting the cancellation window
  /// (from backend configuration) from the booking start time
  static DateTime getCancellationDeadline(
    Booking booking,
    PitchSettings pitchSettings,
  ) {
    return booking.slotStartTime.subtract(
      Duration(hours: pitchSettings.cancellationWindowHours),
    );
  }

  /// Checks if a booking status allows cancellation
  ///
  /// Only confirmed and pending payment bookings can be cancelled
  static bool isValidStatusForCancellation(BookingStatus status) {
    return status == BookingStatus.confirmed ||
        status == BookingStatus.pendingPayment;
  }

  /// Gets the time remaining before cancellation deadline
  ///
  /// Returns Duration.zero if the deadline has passed
  static Duration getCancellationTimeRemaining(
    Booking booking,
    PitchSettings pitchSettings,
    DateTime currentTime,
  ) {
    final deadline = getCancellationDeadline(booking, pitchSettings);
    final timeRemaining = deadline.difference(currentTime);

    // Return zero if deadline has passed (no negative durations)
    return timeRemaining.isNegative ? Duration.zero : timeRemaining;
  }

  /// Determines if a booking can be deleted from the user's local view
  ///
  /// This is for past bookings only - allows users to clean up their
  /// booking history without affecting backend data integrity.
  ///
  /// Only past bookings (where the booking has ended) can be deleted
  /// from the local view, regardless of their status.
  static bool canDeleteBookingFromView(Booking booking, DateTime currentTime) {
    // Can only delete bookings that have completely ended
    return booking.slotEndTime.isBefore(currentTime);
  }
}
