// lib/features/booking/domain/booking_model.dart

enum BookingStatus {
  pendingPayment,
  confirmed,
  cancelledByUser,
  cancelledByAdmin,
  completed,
  noShow;

  static BookingStatus fromString(String status) {
    return BookingStatus.values.firstWhere(
      (e) => e.name.toLowerCase() == status.toLowerCase(),
      orElse: () => BookingStatus.confirmed, // Default fallback
    );
  }

  String toJson() => name;
}

class Booking {
  final int id;
  final String userId; // Make non-nullable since all bookings must have a user
  final int pitchId; // Make non-nullable since all bookings must have a pitch
  final DateTime slotStartTime; // Make non-nullable
  final DateTime slotEndTime; // Make non-nullable
  final BookingStatus status; // Use enum instead of String
  final DateTime createdAt; // Make non-nullable
  final DateTime updatedAt; // Make non-nullable

  const Booking({
    required this.id,
    required this.userId,
    required this.pitchId,
    required this.slotStartTime,
    required this.slotEndTime,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Booking.fromJson(Map<String, dynamic> json) {
    // Defensive null checking with helpful error messages
    final id = json['id'];
    if (id == null) {
      throw FormatException('Booking id cannot be null in JSON: $json');
    }
    
    final userId = json['user_id'];
    if (userId == null) {
      throw FormatException('Booking user_id cannot be null in JSON: $json');
    }
    
    final pitchId = json['pitch_id'];
    if (pitchId == null) {
      throw FormatException('Booking pitch_id cannot be null in JSON: $json');
    }
    
    final slotStartTime = json['slot_start_time'];
    if (slotStartTime == null) {
      throw FormatException('Booking slot_start_time cannot be null in JSON: $json');
    }
    
    final slotEndTime = json['slot_end_time'];
    if (slotEndTime == null) {
      throw FormatException('Booking slot_end_time cannot be null in JSON: $json');
    }
    
    final status = json['status'];
    if (status == null) {
      throw FormatException('Booking status cannot be null in JSON: $json');
    }
    
    final createdAt = json['created_at'];
    if (createdAt == null) {
      throw FormatException('Booking created_at cannot be null in JSON: $json');
    }
    
    final updatedAt = json['updated_at'];
    if (updatedAt == null) {
      throw FormatException('Booking updated_at cannot be null in JSON: $json');
    }
    
    return Booking(
      id: id is int ? id : int.parse(id.toString()),
      userId: userId.toString(),
      pitchId: pitchId is int ? pitchId : int.parse(pitchId.toString()),
      slotStartTime: DateTime.parse(slotStartTime.toString()),
      slotEndTime: DateTime.parse(slotEndTime.toString()),
      status: BookingStatus.fromString(status.toString()),
      createdAt: DateTime.parse(createdAt.toString()),
      updatedAt: DateTime.parse(updatedAt.toString()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'pitch_id': pitchId,
      'slot_start_time': slotStartTime.toIso8601String(),
      'slot_end_time': slotEndTime.toIso8601String(),
      'status': status.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Booking copyWith({
    int? id,
    String? userId,
    int? pitchId,
    DateTime? slotStartTime,
    DateTime? slotEndTime,
    BookingStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Booking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      pitchId: pitchId ?? this.pitchId,
      slotStartTime: slotStartTime ?? this.slotStartTime,
      slotEndTime: slotEndTime ?? this.slotEndTime,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Booking &&
        other.id == id &&
        other.userId == userId &&
        other.pitchId == pitchId &&
        other.slotStartTime == slotStartTime &&
        other.slotEndTime == slotEndTime &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      pitchId,
      slotStartTime,
      slotEndTime,
      status,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'Booking(id: $id, userId: $userId, pitchId: $pitchId, slotStartTime: $slotStartTime, slotEndTime: $slotEndTime, status: $status, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
