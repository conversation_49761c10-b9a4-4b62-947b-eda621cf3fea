import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
// For date formatting
import 'package:skillz/core/utils/date_formatters.dart'; // Updated import
import 'package:skillz/core/routing/route_constants.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/domain/real_time_availability_params.dart';
import 'package:skillz/features/availability/application/real_time_availability_provider.dart'
    as real_time;
// Pitch settings are accessed through availability service providers
import 'package:skillz/features/booking/application/optimistic_booking_service.dart';
import 'package:skillz/features/booking/data/server_side_booking_repository.dart'; // Updated import for server-side providers
import 'package:skillz/features/booking/domain/booking_exceptions.dart'; // Import custom exceptions

class BookingConfirmationScreen extends ConsumerWidget {
  final String pitchId; // Or your Pitch model
  final String pitchName;
  final DateTime selectedDate;
  final DateTime slotStartTime;
  final DateTime slotEndTime;
  // TODO: Potentially pass the TimeSlotInfo object directly

  const BookingConfirmationScreen({
    super.key,
    required this.pitchId,
    required this.pitchName,
    required this.selectedDate,
    required this.slotStartTime,
    required this.slotEndTime,
  });

  /// @deprecated Use AppRoutes.bookingConfirmationName instead
  static const routeName = AppRoutes.bookingConfirmationName;

  /// @deprecated Use AppRoutes.bookingConfirmationPath instead
  static const routePath = AppRoutes.bookingConfirmationPath;

  void _showBookingSuccessDialog(
    BuildContext context,
    WidgetRef ref,
    int? pitchId,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder:
          (BuildContext dialogContext) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 28),
                SizedBox(width: 12),
                Text('Booking Confirmed!'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Your booking has been successfully created.'),
                const SizedBox(height: 16),
                Text(
                  'Pitch: $pitchName',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  'Date: ${AppDateFormats.dayMonthYear.format(selectedDate)}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  'Time: ${AppDateFormats.hourMinute.format(slotStartTime)} - ${AppDateFormats.hourMinute.format(slotEndTime)}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop(); // Close dialog
                  context.pop(); // Go back to AvailabilityScreen
                },
                child: const Text('Done'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(dialogContext).pop(); // Close dialog
                  context
                      .pop(); // Go back to AvailabilityScreen for another booking
                  // The AvailabilityScreen will be refreshed due to the provider invalidation
                },
                icon: const Icon(Icons.add),
                label: const Text('Book Another'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optimisticBookingState = ref.watch(optimisticBookingServiceProvider);
    // final dateFormat = DateFormat('EEE, MMM d, yyyy'); // Removed
    // final timeFormat = DateFormat('HH:mm'); // Removed

    ref.listen<OptimisticBookingState>(optimisticBookingServiceProvider, (
      _,
      state,
    ) {
      if (state.isSuccess) {
        // Clear optimistic booking state on successful booking
        ref
            .read(real_time.slotMarkingStateProvider.notifier)
            .clearOptimisticBooking(slotStartTime, slotEndTime);

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Booking successful!')));
        // Invalidate providers to refresh data
        final currentPitchId = int.tryParse(pitchId);
        if (currentPitchId != null) {
          // Invalidate static availability provider
          ref.invalidate(
            availableSlotsProvider((
              date:
                  selectedDate, // This is BookingConfirmationScreen's selectedDate
              pitchId: currentPitchId,
            )),
          );

          // Invalidate real-time availability provider to ensure immediate update
          ref.invalidate(
            real_time.realTimeAvailabilityProvider(
              RealTimeAvailabilityParams(
                date: selectedDate,
                pitchId: currentPitchId,
              ),
            ),
          );
        } else {
          // Handle error: pitchId is not a valid integer
          // This case should ideally be prevented by validation before this point
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Error: Invalid pitch ID for refreshing slots.'),
              ),
            );
          }
        }
        // Invalidate server-side providers to refresh data
        ref.invalidate(categorizedBookingsProvider);
        ref.invalidate(activeBookingCountProvider);
        ref.read(optimisticBookingServiceProvider.notifier).resetState();

        // Show success dialog with "Book Another" option
        if (context.mounted) {
          _showBookingSuccessDialog(context, ref, currentPitchId);
        }
      } else if (state.hasError) {
        // Clear optimistic booking state on error
        ref
            .read(real_time.slotMarkingStateProvider.notifier)
            .clearOptimisticBooking(slotStartTime, slotEndTime);

        String displayMessage = state.message ?? 'An error occurred';
        Color snackBarColor = Colors.redAccent; // Default error color

        // Handle specific exceptions with tailored messages and styling
        if (state.exception is SlotUnavailableException) {
          displayMessage =
              'Sorry, this slot was just booked by someone else. Please select another.';
          snackBarColor =
              Colors.orange; // Different color for slot unavailability
        } else if (state.exception is BookingLimitReachedException) {
          displayMessage =
              (state.exception as BookingLimitReachedException).message;
          snackBarColor = Colors.amber; // Different color for limit reached

          // Show SnackBar with "View Bookings" action for booking limit errors
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(displayMessage),
              backgroundColor: snackBarColor,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              action: SnackBarAction(
                label: 'View Bookings',
                textColor: Colors.white,
                onPressed: () {
                  context.go(AppRoutes.myBookingsPath);
                },
              ),
            ),
          );
          ref.read(optimisticBookingServiceProvider.notifier).resetState();
          return; // Early return to avoid showing another SnackBar
        } else if (state.exception is GeneralBookingFailureException) {
          displayMessage =
              (state.exception as GeneralBookingFailureException).message;
          snackBarColor = Colors.redAccent; // Standard error color
        } else if (state.isConflict) {
          // Handle conflict state
          displayMessage = state.message ?? 'Booking conflict occurred';
          snackBarColor = Colors.orange;
        } else {
          // Fallback for other exceptions or generic error messages
          if (displayMessage.startsWith("Exception: ")) {
            displayMessage = displayMessage.substring("Exception: ".length);
          }
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(displayMessage),
            backgroundColor: snackBarColor,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        ref.read(optimisticBookingServiceProvider.notifier).resetState();
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('Confirm Booking')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Please confirm your booking details:',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            Text(
              'Pitch: $pitchName',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Date: ${AppDateFormats.dayMonthYear.format(selectedDate)}', // Updated usage
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Time: ${AppDateFormats.hourMinute.format(slotStartTime)} - ${AppDateFormats.hourMinute.format(slotEndTime)}', // Updated usage
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            // Display pricing information
            Builder(
              builder: (context) {
                final pitchIdInt = int.tryParse(pitchId);
                if (pitchIdInt == null) {
                  return Text(
                    'Price: Error - Invalid pitch ID',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  );
                }

                return ref
                    .watch(pitchSettingsProvider(pitchIdInt))
                    .when(
                      data: (settings) {
                        final slotDurationMinutes =
                            slotEndTime.difference(slotStartTime).inMinutes;
                        final durationHours = slotDurationMinutes / 60.0;
                        final slotPrice = settings.pricePerHour * durationHours;
                        return Text(
                          'Price: MWK ${slotPrice.toStringAsFixed(2)}',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        );
                      },
                      loading:
                          () => Text(
                            'Price: Loading...',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                      error:
                          (error, stack) => Text(
                            'Price: Error loading',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                    );
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Football included',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontStyle: FontStyle.italic),
            ),
            const SizedBox(height: 30),
            if (optimisticBookingState.isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              ElevatedButton(
                onPressed:
                    optimisticBookingState
                            .isLoading // Disable button when loading
                        ? null
                        : () {
                          final currentPitchId = int.tryParse(pitchId);
                          if (currentPitchId == null) {
                            // Handle error: pitchId is not a valid integer. Show a message or log.
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Error: Invalid pitch information.',
                                ),
                              ),
                            );
                            return;
                          }
                          ref
                              .read(optimisticBookingServiceProvider.notifier)
                              .createBookingOptimistic(
                                pitchId: currentPitchId,
                                slotStartTime: slotStartTime,
                                slotEndTime: slotEndTime,
                              );
                        },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50), // Full width
                ),
                child: const Text('Confirm & Proceed to Book'),
              ),
              const SizedBox(height: 12),
              OutlinedButton(
                onPressed:
                    optimisticBookingState
                            .isLoading // Disable button when loading
                        ? null
                        : () {
                          // Clear optimistic booking state when cancelling
                          ref
                              .read(optimisticBookingServiceProvider.notifier)
                              .cancelOptimisticBooking();

                          // Use context.pop() for consistency with GoRouter
                          if (GoRouter.of(context).canPop()) {
                            context.pop();
                          } else {
                            // Fallback or error handling if cannot pop - though with a base route, should be possible
                            // TODO: Use proper logging service instead of print
                            debugPrint(
                              "Cannot pop from BookingConfirmationScreen via Cancel button",
                            );
                          }
                        },
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50), // Full width
                ),
                child: const Text('Cancel'),
              ),
            ],
            // Optionally, display the error from bookingCreationState here too if needed
            // if (bookingCreationState is BookingCreationError) ...
          ],
        ),
      ),
    );
  }
}
