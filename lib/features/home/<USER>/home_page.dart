import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:go_router/go_router.dart';
import 'package:skillz/core/utils/logger_service.dart';
import 'package:skillz/core/routing/route_constants.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logger = AppLogger();
    logger.d('DEBUG: HomePage build method called.');
    return Scaffold(
      appBar: AppBar(
        title: const Text('skillz Home'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              try {
                await ref.read(authServiceProvider).signOut();
                // AuthGate will handle navigation
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Logout failed: ${e.toString()}')),
                  );
                }
              }
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Welcome to Skillz Pitch Booking!'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                logger.d(
                  'DEBUG: HomePage - "View Pitch Availability" button pressed. Navigating to ${AppRoutes.availabilityPath}.',
                );
                context.go(AppRoutes.availabilityPath);
              },
              child: const Text('View Pitch Availability'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                logger.d(
                  'DEBUG: HomePage - "My Bookings" button pressed. Navigating to ${AppRoutes.myBookingsPath}.',
                );
                context.go(AppRoutes.myBookingsPath);
              },
              child: const Text('My Bookings'),
            ),
          ],
        ),
      ),
    );
  }
}
