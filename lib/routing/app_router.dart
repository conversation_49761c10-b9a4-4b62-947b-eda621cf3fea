import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:skillz/features/auth/presentation/login_page.dart';
import 'package:skillz/features/auth/presentation/sign_up_page.dart';
import 'package:skillz/features/home/<USER>/home_page.dart';
import 'package:skillz/features/availability/presentation/availability_screen.dart';
import 'package:skillz/features/booking/presentation/my_bookings_screen.dart';
import 'package:skillz/features/booking/presentation/booking_confirmation_screen.dart';
import 'package:skillz/core/utils/logger_service.dart'; // Import the logger service
import 'package:skillz/core/routing/route_constants.dart'; // Import route constants

// Provider to expose the GoRouter instance
final goRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateChangesProvider);

  // Create a simple Listenable (ValueNotifier) for GoRouter refresh
  // We don't care about its value, just that it can notify listeners
  final refreshNotifier = ValueNotifier<int>(0);
  ref.onDispose(refreshNotifier.dispose);

  // Listen to auth state changes and trigger the notifier to force
  // GoRouter to re-evaluate the redirect
  ref.listen(authStateChangesProvider, (previous, next) {
    // Trigger notification by changing the value
    refreshNotifier.value++;
  });

  final logger = AppLogger(); // Create a logger instance

  logger.d(
    'DEBUG: goRouterProvider rebuilding. AuthState: ${authState.valueOrNull}, AuthState hasError: ${authState.hasError}, AuthState isLoading: ${authState.isLoading}',
  );

  return GoRouter(
    initialLocation: AppRoutes.homePath,
    refreshListenable: refreshNotifier, // Use the notifier from its provider
    redirect: (BuildContext context, GoRouterState state) {
      // Handle authentication state with proper AsyncValue pattern
      return authState.when(
        data: (user) {
          final bool loggedIn = user != null;
          final String currentRoute = state.uri.toString();
          logger.d(
            'DEBUG: GoRouter redirect triggered. Current route: $currentRoute, LoggedIn: $loggedIn',
          );

          final bool loggingIn =
              currentRoute == AppRoutes.loginPath ||
              currentRoute == AppRoutes.signupPath;

          // If not logged in and not on login/signup, redirect to login
          if (!loggedIn && !loggingIn) {
            logger.d('DEBUG: Redirecting to ${AppRoutes.loginPath}');
            return AppRoutes.loginPath;
          }
          // If logged in and on login/signup, redirect to home
          if (loggedIn && loggingIn) {
            logger.d('DEBUG: Redirecting to ${AppRoutes.homePath}');
            return AppRoutes.homePath;
          }
          logger.d('DEBUG: No redirect needed.');
          return null; // No redirect needed
        },
        loading: () {
          // Don't redirect while loading auth state
          logger.d('DEBUG: Auth state loading, no redirect');
          return null;
        },
        error: (error, stackTrace) {
          // On auth error, redirect to login
          logger.e('DEBUG: Auth error, redirecting to login: $error');
          return AppRoutes.loginPath;
        },
      );
    },
    routes: [
      GoRoute(
        path: AppRoutes.homePath,
        name: AppRoutes.homeName,
        builder: (context, state) {
          logger.d('DEBUG: Building HomePage route (${AppRoutes.homePath})');
          return const HomePage();
        },
        routes: [
          GoRoute(
            path: 'availability', // Accessed as /availability
            name: AppRoutes.availabilityName,
            builder: (context, state) {
              logger.d(
                'DEBUG: Building AvailabilityScreen route (${AppRoutes.availabilityPath})',
              );
              // Removed pitchId logic
              return const AvailabilityScreen(); // Instantiate without pitchId
            },
          ),
          GoRoute(
            path: 'my-bookings', // Accessed as /my-bookings
            name: AppRoutes.myBookingsName,
            builder: (context, state) {
              logger.d(
                'DEBUG: Building MyBookingsScreen route (${AppRoutes.myBookingsPath})',
              );
              return const MyBookingsScreen();
            },
          ),
          GoRoute(
            path: BookingConfirmationScreen.routePath.substring(
              1,
            ), // Remove leading '/'
            name: AppRoutes.bookingConfirmationName,
            builder: (context, state) {
              // TODO: Extract parameters safely and pass them to BookingConfirmationScreen
              // For now, using placeholders or default values
              final pitchId =
                  state.uri.queryParameters['pitchId'] ?? 'defaultPitchId';
              final pitchName =
                  state.uri.queryParameters['pitchName'] ?? 'Default Pitch';
              final selectedDateString =
                  state.uri.queryParameters['selectedDate'];
              final slotStartTimeString =
                  state.uri.queryParameters['slotStartTime'];
              final slotEndTimeString =
                  state.uri.queryParameters['slotEndTime'];

              // Basic error handling/defaults for dates - robust parsing needed
              final selectedDate =
                  selectedDateString != null
                      ? DateTime.tryParse(selectedDateString)
                      : DateTime.now();
              final slotStartTime =
                  slotStartTimeString != null
                      ? DateTime.tryParse(slotStartTimeString)
                      : DateTime.now();
              final slotEndTime =
                  slotEndTimeString != null
                      ? DateTime.tryParse(slotEndTimeString)
                      : DateTime.now().add(const Duration(hours: 1));

              if (selectedDate == null ||
                  slotStartTime == null ||
                  slotEndTime == null) {
                // Handle error: perhaps redirect to an error page or show a message
                logger.e(
                  'Error parsing date/time for BookingConfirmationScreen',
                );
                return Scaffold(
                  body: Center(
                    child: Text('Error: Invalid booking details provided.'),
                  ),
                );
              }

              logger.d(
                'DEBUG: Building BookingConfirmationScreen route (${BookingConfirmationScreen.routePath})',
              );
              return BookingConfirmationScreen(
                pitchId: pitchId,
                pitchName: pitchName,
                selectedDate: selectedDate,
                slotStartTime: slotStartTime,
                slotEndTime: slotEndTime,
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.loginPath,
        name: AppRoutes.loginName,
        builder: (context, state) {
          logger.d('DEBUG: Building LoginPage route (${AppRoutes.loginPath})');
          return const LoginPage();
        },
      ),
      GoRoute(
        path: AppRoutes.signupPath,
        name: AppRoutes.signupName,
        builder: (context, state) {
          logger.d(
            'DEBUG: Building SignUpPage route (${AppRoutes.signupPath})',
          );
          return const SignUpPage();
        },
      ),
    ],
    errorBuilder:
        (context, state) => Scaffold(
          body: Center(child: Text('Page not found: ${state.error}')),
        ),
  );
});
