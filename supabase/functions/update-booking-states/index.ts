import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";

interface BookingUpdateResult {
  transitioned_bookings: number;
  updated_slots: number;
  cancelled_pending: number;
  errors: string[];
}

Deno.serve(async (req: Request) => {
  try {
    // Get Supabase environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing Supabase configuration',
          details: 'SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY not found'
        }), 
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const result: BookingUpdateResult = {
      transitioned_bookings: 0,
      updated_slots: 0,
      cancelled_pending: 0,
      errors: []
    };

    const now = new Date().toISOString();

    try {
      // 1. Transition confirmed bookings that have ended to completed
      const { data: pastBookings, error: transitionError } = await supabase
        .from('bookings')
        .update({ 
          status: 'completed',
          updated_at: now
        })
        .eq('status', 'confirmed')
        .lt('slot_end_time', now)
        .select('id');

      if (transitionError) {
        result.errors.push(`Booking transition error: ${transitionError.message}`);
      } else {
        result.transitioned_bookings = pastBookings?.length || 0;
      }

    } catch (error) {
      result.errors.push(`Exception during booking transition: ${error.message}`);
    }

    try {
      // 2. Mark past time slots as unavailable
      const { data: updatedSlots, error: slotError } = await supabase
        .from('time_slots')
        .update({ 
          is_available: false,
          updated_at: now
        })
        .eq('is_available', true)
        .lt('end_time', now)
        .select('id');

      if (slotError) {
        result.errors.push(`Slot update error: ${slotError.message}`);
      } else {
        result.updated_slots = updatedSlots?.length || 0;
      }

    } catch (error) {
      result.errors.push(`Exception during slot update: ${error.message}`);
    }

    try {
      // 3. Cancel pending payment bookings that are past their slot time
      const { data: cancelledBookings, error: cancelError } = await supabase
        .from('bookings')
        .update({ 
          status: 'cancelled',
          updated_at: now
        })
        .eq('status', 'pending_payment')
        .lt('slot_end_time', now)
        .select('id');

      if (cancelError) {
        result.errors.push(`Cancel pending error: ${cancelError.message}`);
      } else {
        result.cancelled_pending = cancelledBookings?.length || 0;
      }

    } catch (error) {
      result.errors.push(`Exception during pending cancellation: ${error.message}`);
    }

    // Return summary
    const response = {
      success: result.errors.length === 0,
      timestamp: now,
      results: result,
      message: `Processed ${result.transitioned_bookings} booking transitions, ${result.updated_slots} slot updates, ${result.cancelled_pending} pending cancellations`
    };

    return new Response(
      JSON.stringify(response, null, 2), 
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Edge function error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message,
        timestamp: new Date().toISOString()
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
});
