-- Migration: Add missing booking_state_health_check function
-- This function was identified as missing during ADR-008 verification
-- It provides monitoring and diagnostics for the booking state management system

CREATE OR REPLACE FUNCTION booking_state_health_check()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_bookings INTEGER;
    active_bookings INTEGER;
    past_bookings INTEGER;
    pending_bookings INTEGER;
    expired_confirmed INTEGER;
    expired_pending INTEGER;
    future_bookings INTEGER;
    health_status TEXT := 'healthy';
    warnings TEXT[] := ARRAY[]::TEXT[];
    result JSON;
BEGIN
    -- Get total booking count
    SELECT COUNT(*) INTO total_bookings FROM bookings;
    
    -- Get active bookings (confirmed and future)
    SELECT COUNT(*) INTO active_bookings 
    FROM bookings 
    WHERE status = 'confirmed' AND slot_end_time > NOW();
    
    -- Get past bookings (completed or past slot time)
    SELECT COUNT(*) INTO past_bookings 
    FROM bookings 
    WHERE status = 'completed' OR slot_end_time <= NOW();
    
    -- Get pending payment bookings
    SELECT COUNT(*) INTO pending_bookings 
    FROM bookings 
    WHERE status = 'pending_payment';
    
    -- Check for confirmed bookings that should be completed (expired)
    SELECT COUNT(*) INTO expired_confirmed 
    FROM bookings 
    WHERE status = 'confirmed' AND slot_end_time <= NOW();
    
    -- Check for pending payment bookings that are expired
    SELECT COUNT(*) INTO expired_pending 
    FROM bookings 
    WHERE status = 'pending_payment' AND slot_end_time <= NOW();
    
    -- Get future bookings count
    SELECT COUNT(*) INTO future_bookings 
    FROM bookings 
    WHERE slot_start_time > NOW();
    
    -- Determine health status and warnings
    IF expired_confirmed > 0 THEN
        health_status := 'needs_attention';
        warnings := array_append(warnings, expired_confirmed || ' confirmed bookings need transition to completed');
    END IF;
    
    IF expired_pending > 0 THEN
        health_status := 'needs_attention';
        warnings := array_append(warnings, expired_pending || ' pending payment bookings should be cancelled');
    END IF;
    
    IF total_bookings = 0 THEN
        health_status := 'warning';
        warnings := array_append(warnings, 'No bookings found in system');
    END IF;
    
    -- Build result JSON
    result := json_build_object(
        'status', health_status,
        'timestamp', NOW(),
        'statistics', json_build_object(
            'total_bookings', total_bookings,
            'active_bookings', active_bookings,
            'past_bookings', past_bookings,
            'pending_bookings', pending_bookings,
            'future_bookings', future_bookings
        ),
        'issues', json_build_object(
            'expired_confirmed', expired_confirmed,
            'expired_pending', expired_pending
        ),
        'warnings', warnings,
        'recommendations', CASE 
            WHEN expired_confirmed > 0 OR expired_pending > 0 THEN 
                ARRAY['Run update_booking_states() to fix expired bookings']
            ELSE 
                ARRAY[]::TEXT[]
        END
    );
    
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION booking_state_health_check() TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION booking_state_health_check() IS 'Health check function for booking state management system. Returns status, statistics, and recommendations for maintenance.';
