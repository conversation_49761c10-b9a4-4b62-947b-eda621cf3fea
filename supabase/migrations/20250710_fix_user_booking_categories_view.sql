-- Migration: Fix user_booking_categories view to include missing pitch_id column
-- Date: 2025-07-10
-- Issue: PostgresException - Column 'user_booking_categories.pitch_id' does not exist
-- Solution: Add pitch_id to the view alongside pitch_name

-- Drop the existing view
DROP VIEW IF EXISTS user_booking_categories;

-- Recreate view with pitch_id included
CREATE VIEW user_booking_categories AS
SELECT 
    b.user_id,
    b.id,
    b.pitch_id,  -- ADD: Include pitch_id for Booking model compatibility
    b.status,
    b.created_at,
    b.updated_at,
    b.slot_start_time,
    b.slot_end_time,
    b.booking_date,
    ps.pitch_name,
    ps.price_per_hour,
    CASE
        WHEN b.status = 'confirmed' AND b.slot_end_time > NOW() THEN 'active'
        WHEN b.status = 'confirmed' AND b.slot_end_time <= NOW() THEN 'past'
        WHEN b.status = 'completed' THEN 'past'
        WHEN b.status = 'cancelled' THEN 'cancelled'
        WHEN b.status = 'pending_payment' THEN 'pending'
        ELSE 'other'
    END as booking_category
FROM bookings b
JOIN pitch_settings ps ON b.pitch_id = ps.id;

-- Add comment explaining the view purpose
COMMENT ON VIEW user_booking_categories IS 'ADR-008: Server-side booking categorization with all fields needed for Booking model';
