-- Migration 005: Server-Side Booking State Management
-- Date: July 10, 2025
-- Purpose: Implement server-side booking state views and automatic state transitions
-- Addresses: ADR-008 - Server-side booking state management
-- Fixes: Expired bookings remaining in "active" status

-- =====================================================
-- PHASE 1: Server-Side State Management Views
-- =====================================================

-- View: active_bookings
-- Purpose: Server-side view for active/future bookings only
-- Replaces client-side filtering for booking limits and active counts
CREATE OR REPLACE VIEW active_bookings AS
SELECT 
  b.id,
  b.user_id,
  b.pitch_id,
  b.slot_start_time,
  b.slot_end_time,
  b.status,
  b.created_at,
  b.updated_at,
  -- Server-determined booking category
  CASE 
    WHEN b.slot_start_time > NOW() THEN 'upcoming'
    WHEN b.slot_start_time <= NOW() AND b.slot_end_time > NOW() THEN 'current'
  END as booking_category
FROM bookings b
WHERE b.status IN ('confirmed', 'pending_payment')
  AND b.slot_end_time > NOW(); -- Only count bookings that haven't ended

-- View: user_booking_categories
-- Purpose: Categorize all user bookings by time status with RLS
-- Used by My Bookings screen for consistent categorization
CREATE OR REPLACE VIEW user_booking_categories AS
SELECT 
  b.id,
  b.user_id,
  b.pitch_id,
  b.slot_start_time,
  b.slot_end_time,
  b.status,
  b.created_at,
  b.updated_at,
  -- Server-determined time-based category
  CASE 
    WHEN b.slot_start_time > NOW() THEN 'upcoming'
    WHEN b.slot_start_time <= NOW() AND b.slot_end_time > NOW() THEN 'current'
    WHEN b.slot_end_time <= NOW() THEN 'past'
  END as booking_category
FROM bookings b;

-- =====================================================
-- PHASE 2: Row Level Security for Views
-- =====================================================

-- Enable RLS on the underlying bookings table (should already be enabled)
-- Views inherit RLS from base tables

-- Create additional RLS policies for view-specific access patterns
-- Policy: Allow users to view their own active bookings
CREATE POLICY IF NOT EXISTS "Users can view own active bookings" ON bookings
  FOR SELECT USING (
    auth.uid() = user_id AND 
    status IN ('confirmed', 'pending_payment') AND 
    slot_end_time > NOW()
  );

-- Policy: Allow users to view all their own bookings (for categorization)
CREATE POLICY IF NOT EXISTS "Users can view own booking history" ON bookings
  FOR SELECT USING (auth.uid() = user_id);

-- =====================================================
-- PHASE 3: Automatic State Transition Functions
-- =====================================================

-- Function: update_booking_states
-- Purpose: Automatically transition bookings from active to completed
-- Can be called manually or via scheduled job
CREATE OR REPLACE FUNCTION update_booking_states()
RETURNS TABLE(
  updated_count INTEGER,
  past_due_count INTEGER,
  message TEXT,
  execution_time_ms INTEGER
) 
LANGUAGE plpgsql AS $$
DECLARE
  rows_updated INTEGER := 0;
  rows_past_due INTEGER := 0;
  start_time_ms BIGINT;
  end_time_ms BIGINT;
  current_time TIMESTAMPTZ;
BEGIN
  start_time_ms := EXTRACT(EPOCH FROM clock_timestamp()) * 1000;
  current_time := NOW();
  
  -- Update bookings that have ended to 'completed' status
  UPDATE bookings 
  SET status = 'completed', updated_at = current_time
  WHERE status = 'confirmed' 
    AND slot_end_time <= current_time
    AND slot_end_time > current_time - INTERVAL '7 days'; -- Only process recent completions
  
  GET DIAGNOSTICS rows_updated = ROW_COUNT;
  
  -- Count bookings that are significantly past due (for monitoring)
  SELECT COUNT(*) INTO rows_past_due
  FROM bookings
  WHERE status = 'confirmed'
    AND slot_end_time < current_time - INTERVAL '1 day';
  
  end_time_ms := EXTRACT(EPOCH FROM clock_timestamp()) * 1000;
  
  RETURN QUERY SELECT 
    rows_updated,
    rows_past_due,
    CASE 
      WHEN rows_updated > 0 THEN 'Updated ' || rows_updated || ' bookings to completed status'
      WHEN rows_past_due > 0 THEN 'Warning: ' || rows_past_due || ' bookings are significantly past due'
      ELSE 'No bookings required status updates'
    END,
    (end_time_ms - start_time_ms)::INTEGER;
END;
$$;

-- Function: get_user_active_booking_count
-- Purpose: Server-side function for consistent booking limit enforcement
-- Replaces client-side counting logic
CREATE OR REPLACE FUNCTION get_user_active_booking_count(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER -- Allows function to bypass RLS for counting
AS $$
DECLARE
  target_user_id UUID;
  booking_count INTEGER;
BEGIN
  -- Use provided user_id or default to current authenticated user
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'No user specified and no authenticated user found';
  END IF;
  
  -- Count active bookings (using same logic as active_bookings view)
  SELECT COUNT(*) INTO booking_count
  FROM bookings
  WHERE user_id = target_user_id
    AND status IN ('confirmed', 'pending_payment')
    AND slot_end_time > NOW();
    
  RETURN booking_count;
END;
$$;

-- =====================================================
-- PHASE 4: Performance Optimization
-- =====================================================

-- Index for active_bookings view queries
CREATE INDEX IF NOT EXISTS idx_bookings_active_state 
ON bookings (user_id, status, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');

-- Index for time-based categorization queries
CREATE INDEX IF NOT EXISTS idx_bookings_time_category 
ON bookings (user_id, slot_start_time, slot_end_time, status);

-- Index for state transition function
CREATE INDEX IF NOT EXISTS idx_bookings_completion_candidates
ON bookings (status, slot_end_time)
WHERE status = 'confirmed';

-- =====================================================
-- PHASE 5: Monitoring and Maintenance
-- =====================================================

-- Function: booking_state_health_check
-- Purpose: Health check function for monitoring booking state consistency
CREATE OR REPLACE FUNCTION booking_state_health_check()
RETURNS TABLE(
  check_name TEXT,
  status TEXT,
  count INTEGER,
  details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
  past_due_count INTEGER;
  future_past_count INTEGER;
  orphaned_count INTEGER;
BEGIN
  -- Check 1: Bookings that should have been transitioned to completed
  SELECT COUNT(*) INTO past_due_count
  FROM bookings 
  WHERE status = 'confirmed' 
    AND slot_end_time < NOW() - INTERVAL '1 hour';
  
  RETURN QUERY SELECT 
    'Past Due Bookings'::TEXT,
    CASE WHEN past_due_count = 0 THEN 'OK' ELSE 'WARNING' END,
    past_due_count,
    'Bookings that ended >1 hour ago but still marked as confirmed';
  
  -- Check 2: Bookings with impossible time states
  SELECT COUNT(*) INTO future_past_count
  FROM bookings 
  WHERE slot_start_time > slot_end_time;
  
  RETURN QUERY SELECT 
    'Invalid Time Ranges'::TEXT,
    CASE WHEN future_past_count = 0 THEN 'OK' ELSE 'ERROR' END,
    future_past_count,
    'Bookings with start time after end time';
  
  -- Check 3: Orphaned bookings (user doesn't exist)
  SELECT COUNT(*) INTO orphaned_count
  FROM bookings b
  LEFT JOIN auth.users u ON b.user_id = u.id
  WHERE u.id IS NULL;
  
  RETURN QUERY SELECT 
    'Orphaned Bookings'::TEXT,
    CASE WHEN orphaned_count = 0 THEN 'OK' ELSE 'WARNING' END,
    orphaned_count,
    'Bookings with non-existent user_id';
END;
$$;

-- =====================================================
-- PHASE 6: Grant Permissions
-- =====================================================

-- Grant permissions for authenticated users to use functions
GRANT EXECUTE ON FUNCTION update_booking_states() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_active_booking_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION booking_state_health_check() TO authenticated;

-- Grant SELECT permissions on views (RLS will still apply)
GRANT SELECT ON active_bookings TO authenticated;
GRANT SELECT ON user_booking_categories TO authenticated;

-- =====================================================
-- PHASE 7: Documentation and Comments
-- =====================================================

COMMENT ON VIEW active_bookings IS 'Server-side view for active/future bookings with consistent state logic. Used by ServerSideBookingRepository for booking limits and active counts.';

COMMENT ON VIEW user_booking_categories IS 'Server-side view for user booking categorization by time status. Used by My Bookings screen for consistent past/current/upcoming logic.';

COMMENT ON FUNCTION update_booking_states() IS 'Transitions completed bookings from confirmed to completed status. Can be called manually or via scheduled job for automatic state maintenance.';

COMMENT ON FUNCTION get_user_active_booking_count(UUID) IS 'Server-side function for consistent active booking counting. Used for booking limit enforcement across all application layers.';

COMMENT ON FUNCTION booking_state_health_check() IS 'Health check function for monitoring booking state consistency and identifying data integrity issues.';

-- =====================================================
-- MIGRATION VERIFICATION
-- =====================================================

-- Verify views were created successfully
DO $$
DECLARE
  view_count INTEGER;
  function_count INTEGER;
BEGIN
  -- Check views
  SELECT COUNT(*) INTO view_count
  FROM information_schema.views
  WHERE table_schema = 'public'
    AND table_name IN ('active_bookings', 'user_booking_categories');
  
  IF view_count != 2 THEN
    RAISE EXCEPTION 'Migration failed: Expected 2 views, found %', view_count;
  END IF;
  
  -- Check functions
  SELECT COUNT(*) INTO function_count
  FROM pg_proc p
  JOIN pg_namespace n ON p.pronamespace = n.oid
  WHERE n.nspname = 'public'
    AND p.proname IN ('update_booking_states', 'get_user_active_booking_count', 'booking_state_health_check');
  
  IF function_count != 3 THEN
    RAISE EXCEPTION 'Migration failed: Expected 3 functions, found %', function_count;
  END IF;
  
  RAISE NOTICE 'Migration 005 completed successfully: % views, % functions created', view_count, function_count;
END $$;

-- =====================================================
-- IMMEDIATE HEALTH CHECK
-- =====================================================

-- Run initial health check to identify any existing data issues
SELECT * FROM booking_state_health_check();

-- Show current active booking counts by user (for verification)
SELECT 
  u.email,
  get_user_active_booking_count(u.id) as active_count,
  COUNT(CASE WHEN slot_end_time > NOW() THEN 1 END) as manual_count
FROM auth.users u
LEFT JOIN bookings b ON u.id = b.user_id AND b.status IN ('confirmed', 'pending_payment')
GROUP BY u.id, u.email
HAVING get_user_active_booking_count(u.id) > 0 OR COUNT(b.id) > 0
ORDER BY active_count DESC;
