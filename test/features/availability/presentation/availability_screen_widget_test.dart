import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/core/utils/date_formatters.dart'; // Added import for AppDateFormats
import 'package:skillz/features/booking/application/optimistic_booking_service.dart';
import 'package:skillz/features/availability/presentation/availability_screen.dart';
import 'package:skillz/features/booking/data/booking_repository.dart'; // For userBookingsProvider
import 'package:mocktail/mocktail.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart'; // Correct: Import PitchSettings
import 'package:skillz/features/availability/domain/real_time_availability_params.dart';
import 'package:skillz/features/booking/domain/booking_model.dart'; // Import Booking model
import 'package:skillz/features/availability/application/real_time_availability_provider.dart'
    as real_time;
import 'package:skillz/features/availability/application/real_time_availability_simple.dart'
    as simple_real_time;
import '../../../test_helpers/supabase_mock_helper.dart'; // Use the new helper
import '../../../test_helpers/widget_test_helper.dart'; // Use the widget test helper

// Mocks
class MockOptimisticBookingService extends OptimisticBookingService {
  @override
  OptimisticBookingState build() {
    return const OptimisticBookingState(status: OptimisticBookingStatus.idle);
  }

  void setTestState(OptimisticBookingState newState) {
    state = newState;
  }
}

void main() {
  final testDate = DateTime(2024, 1, 1);
  final testPitchId1 = 1; // Default pitch for many tests
  final testPitchId2 = 2;

  // Create a default PitchSettings for tests
  final mockPitchSettings1 = PitchSettings(
    id: testPitchId1,
    name: 'Test Pitch 1',
    openTime: "09:00", // Ensure format matches AppDateFormats.hourMinute
    closeTime: "22:00", // Ensure format matches AppDateFormats.hourMinute
    slotDurationMinutes: 60,
    cancellationWindowHours: 24,
    pricePerHour: 5000.0, // Default test price
    maxBookingsPerUser: 4, // Default test booking limit
    isEnabled: true, // Added required isEnabled field
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  final mockPitchSettings2 = PitchSettings(
    id: testPitchId2,
    name: 'Test Pitch 2',
    openTime: "10:00", // Ensure format matches AppDateFormats.hourMinute
    closeTime: "20:00", // Ensure format matches AppDateFormats.hourMinute
    slotDurationMinutes: 30,
    cancellationWindowHours: 48,
    pricePerHour: 7500.0, // Different test price
    maxBookingsPerUser: 6, // Different test booking limit
    isEnabled: true, // Added required isEnabled field
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  // Updated: Use PitchSettings for the list
  final mockPitchSettingsList = [mockPitchSettings1, mockPitchSettings2];

  setUpAll(() async {
    // Initialize Supabase mocks using the new helper
    await setupWidgetTest();

    registerFallbackValue(
      const OptimisticBookingState(status: OptimisticBookingStatus.idle),
    );
    registerFallbackValue(const Duration(milliseconds: 10));
    // Register fallback for BookingsForDateParams and AvailableSlotsParams
    registerFallbackValue((date: DateTime.now(), pitchId: 1)); // Removed cast
    registerFallbackValue((date: DateTime.now(), pitchId: 1)); // Removed cast
  });

  tearDownAll(() {
    tearDownSupabaseMocks();
  });

  List<TimeSlotInfo> createSlots({
    required int count,
    required int bookedCount,
    required DateTime date,
  }) {
    final slots = <TimeSlotInfo>[];
    for (int i = 0; i < count; i++) {
      slots.add(
        TimeSlotInfo(
          startTime: date.add(Duration(hours: 9 + i)),
          endTime: date.add(Duration(hours: 10 + i)),
          isBooked: i < bookedCount,
          bookingId: i < bookedCount ? 'booking_${i + 1}' : null,
        ),
      );
    }
    return slots;
  }

  Widget createTestWidget({
    DateTime? date,
    AsyncValue<List<PitchSettings>>? allPitchesAsyncValue,
    AsyncValue<PitchSettings>? pitchSettingsAsyncValue,
    AsyncValue<List<TimeSlotInfo>>? availableSlotsValue,
    AsyncValue<List<Booking>>? userBookingsAsyncValue,
    int? selectedPitchId,
    MockOptimisticBookingService?
    mockBookingService, // Updated to use OptimisticBookingService
  }) {
    final AsyncValue<List<PitchSettings>> effectiveAllPitchesAsyncValue =
        allPitchesAsyncValue ?? AsyncValue.data(mockPitchSettingsList);
    final AsyncValue<PitchSettings> effectivePitchSettingsAsyncValue =
        pitchSettingsAsyncValue ?? AsyncValue.data(mockPitchSettings1);
    final AsyncValue<List<TimeSlotInfo>> effectiveAvailableSlotsValue =
        availableSlotsValue ?? AsyncValue.data([]);
    final AsyncValue<List<Booking>> effectiveUserBookingsAsyncValue =
        userBookingsAsyncValue ?? AsyncValue.data(<Booking>[]);
    final MockOptimisticBookingService effectiveBookingService =
        mockBookingService ?? MockOptimisticBookingService();

    // Calculate effectiveSelectedPitchId only when allPitches has data
    final int? effectiveSelectedPitchId = effectiveAllPitchesAsyncValue.when(
      data: (pitches) {
        if (pitches.isNotEmpty) {
          return selectedPitchId ?? pitches.first.id;
        }
        return null;
      },
      loading: () => null, // Don't set selected pitch when loading
      error: (_, __) => null, // Don't set selected pitch when error
    );

    final DateTime dateForSlotsProvider = date ?? testDate;

    // Create mock providers that return appropriate states without infinite delays
    return ProviderScope(
      overrides: [
        allPitchesProvider.overrideWith((ref) {
          if (effectiveAllPitchesAsyncValue.isLoading) {
            // For loading tests, return a Future that completes quickly for testing
            return Future<List<PitchSettings>>.delayed(
              const Duration(milliseconds: 100), // Short delay for testing
              () => effectiveAllPitchesAsyncValue.value ?? <PitchSettings>[],
            );
          }
          if (effectiveAllPitchesAsyncValue.hasError) {
            return Future.error(effectiveAllPitchesAsyncValue.error!);
          }
          return Future.value(
            effectiveAllPitchesAsyncValue.value ?? <PitchSettings>[],
          );
        }),
        // Only override pitch-specific providers when we have a valid pitch ID
        if (effectiveSelectedPitchId != null) ...[
          selectedPitchIdProvider.overrideWith(
            (ref) => effectiveSelectedPitchId,
          ),
          pitchSettingsProvider(effectiveSelectedPitchId).overrideWith((ref) {
            if (effectivePitchSettingsAsyncValue.isLoading) {
              return Future<PitchSettings>.delayed(
                const Duration(milliseconds: 100), // Short delay for testing
                () =>
                    effectivePitchSettingsAsyncValue.value ??
                    mockPitchSettings1,
              );
            }
            if (effectivePitchSettingsAsyncValue.hasError) {
              return Future.error(effectivePitchSettingsAsyncValue.error!);
            }
            return Future.value(
              effectivePitchSettingsAsyncValue.value ?? mockPitchSettings1,
            );
          }),
          availableSlotsProvider((
            date: dateForSlotsProvider,
            pitchId: effectiveSelectedPitchId,
          )).overrideWith((ref) {
            if (effectiveAvailableSlotsValue.isLoading) {
              return Future<List<TimeSlotInfo>>.delayed(
                const Duration(milliseconds: 100), // Short delay for testing
                () => effectiveAvailableSlotsValue.value ?? [],
              );
            }
            if (effectiveAvailableSlotsValue.hasError) {
              return Future.error(effectiveAvailableSlotsValue.error!);
            }
            return Future.value(effectiveAvailableSlotsValue.value ?? []);
          }),
        ],
        optimisticBookingServiceProvider.overrideWith(
          () => effectiveBookingService,
        ),
        // Override for userBookingsProvider
        userBookingsProvider.overrideWith((ref) {
          if (effectiveUserBookingsAsyncValue.isLoading) {
            return Future<List<Booking>>.delayed(
              const Duration(milliseconds: 100), // Short delay for testing
              () => effectiveUserBookingsAsyncValue.value ?? <Booking>[],
            );
          }
          if (effectiveUserBookingsAsyncValue.hasError) {
            return Future.error(effectiveUserBookingsAsyncValue.error!);
          }
          return Future.value(
            effectiveUserBookingsAsyncValue.value ?? <Booking>[],
          );
        }),
        // Override real-time providers to prevent Timer issues in tests
        if (effectiveSelectedPitchId != null) ...[
          real_time
              .realTimeAvailabilityProvider(
                RealTimeAvailabilityParams(
                  date: dateForSlotsProvider,
                  pitchId: effectiveSelectedPitchId,
                ),
              )
              .overrideWith((ref) {
                // Return a static stream based on the available slots value
                return Stream.fromIterable([
                  effectiveAvailableSlotsValue.value ?? [],
                ]);
              }),
          simple_real_time
              .simpleRealTimeAvailabilityProvider(
                RealTimeAvailabilityParams(
                  date: dateForSlotsProvider,
                  pitchId: effectiveSelectedPitchId,
                ),
              )
              .overrideWith((ref) {
                // Return a static stream based on the available slots value
                return Stream.fromIterable([
                  effectiveAvailableSlotsValue.value ?? [],
                ]);
              }),
        ],
        // Override optimistic booking state provider to prevent state issues
        real_time.slotMarkingStateProvider.overrideWith(() {
          return real_time.SlotMarkingState();
        }),
        simple_real_time.simpleSlotMarkingStateProvider.overrideWith(() {
          return simple_real_time.SimpleSlotMarkingState();
        }),
      ],
      child: MaterialApp(
        home: AvailabilityScreen(
          initialDate: date ?? testDate,
          // pitchId removed from constructor
        ),
      ),
    );
  }

  group('AvailabilityScreen Widget Tests', () {
    testWidgets('renders loading indicator when allPitches are loading', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(
          allPitchesAsyncValue: const AsyncLoading(),
          availableSlotsValue: const AsyncData(
            [],
          ), // Provide a default for other providers
        ),
      );
      // The DropdownButtonFormField will show a CircularProgressIndicator
      // And the main content area might show "Please select a pitch..." or its own loader
      expect(
        find.byType(CircularProgressIndicator),
        findsWidgets,
      ); // Might find more than one
      await tester.pumpAndSettle();
    });

    // testWidgets('renders error message when allPitches fail to load', (
    //   WidgetTester tester,
    // ) async {
    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncError(
    //         "Failed to load pitches",
    //         StackTrace.empty,
    //       ),
    //       availableSlotsValue: const AsyncData([]),
    //     ),
    //   );
    //   await tester.pumpAndSettle();
    //   expect(
    //     find.textContaining("Error loading pitches: Failed to load pitches"),
    //     findsOneWidget,
    //   );
    // });

    testWidgets('renders "No pitches available" when allPitches is empty', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(
          allPitchesAsyncValue: const AsyncData([]),
          availableSlotsValue: const AsyncData([]),
        ),
      );
      await tester.pumpAndSettle();
      expect(find.text("No pitches available."), findsOneWidget);
      // Also, the main content area should prompt to select a pitch, but there are none.
      expect(
        find.text("Please select a pitch to see availability."),
        findsOneWidget,
      );
    });

    testWidgets(
      'selects first pitch and loads its settings and slots if selectedPitchIdProvider is null initially',
      (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
            selectedPitchId: null, // Explicitly null
            // pitchSettingsAsyncValue will be for mockPitchSettingsList.first.id
            pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
            availableSlotsValue: const AsyncData([]), // For pitch 1
          ),
        );

        await tester
            .pumpAndSettle(); // Allow providers to resolve and UI to update

        // Check if dropdown is populated and shows the first pitch's name
        expect(
          find.widgetWithText(
            DropdownButtonFormField<int>,
            mockPitchSettings1.name,
          ),
          findsOneWidget,
        );
        // Check if settings for the first pitch are displayed (e.g., its name in the title)
        // The title format is "Available Slots on {Date} for {Pitch Name}:"
        // Date is formatted using AppDateFormats.yearMonthDay
        expect(
          find.textContaining(
            'Available Slots on ${AppDateFormats.yearMonthDay.format(testDate.toLocal())} for ${mockPitchSettings1.name}:',
          ),
          findsOneWidget,
        );
      },
    );

    testWidgets(
      'renders loading indicator when pitch settings are loading (after pitch selection)',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
            selectedPitchId: testPitchId1,
            pitchSettingsAsyncValue:
                const AsyncLoading(), // Loading for selected pitch
            availableSlotsValue: const AsyncData(
              [],
            ), // Default, won't be shown yet
          ),
        );
        // await tester.pump(); // Initial pump
        // The UI shows a loader for pitch settings if selectedPitchId is not null but settings are loading.
        // There will be one for the dropdown (if it's also loading, though here it's data)
        // and one for the main content area.
        expect(
          find.byType(CircularProgressIndicator),
          findsWidgets,
        ); // Expect loader for settings
        await tester.pumpAndSettle();
      },
    );

    testWidgets(
      'renders loading indicator when slots are loading (after pitch and settings loaded)',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
            selectedPitchId: testPitchId1,
            pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
            availableSlotsValue: const AsyncLoading(), // Slots are loading
          ),
        );
        // await tester.pump();
        // Expect loader for available slots
        expect(find.byType(CircularProgressIndicator), findsWidgets);
        await tester.pumpAndSettle();
      },
    );

    // testWidgets('renders error message when slots fail to load', (
    //   WidgetTester tester,
    // ) async {
    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
    //       selectedPitchId: testPitchId1,
    //       pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
    //       availableSlotsValue: AsyncError(
    //         'Failed to load slots',
    //         StackTrace.empty,
    //       ),
    //     ),
    //   );
    //   await tester.pumpAndSettle();
    //   expect(
    //     find.textContaining(
    //       'Error fetching available slots: Exception: Failed to load slots',
    //     ),
    //     findsOneWidget,
    //   );
    // });

    // testWidgets('renders error message when pitch settings fail to load', (
    //   WidgetTester tester,
    // ) async {
    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
    //       selectedPitchId: testPitchId1,
    //       pitchSettingsAsyncValue: AsyncError(
    //         'Failed to load settings',
    //         StackTrace.empty,
    //       ),
    //       availableSlotsValue: const AsyncData(
    //         [],
    //       ), // Slots won't be fetched if settings fail
    //     ),
    //   );
    //   await tester.pumpAndSettle();
    //   expect(
    //     find.textContaining(
    //       'Error fetching pitch settings: Exception: Failed to load settings',
    //     ),
    //     findsOneWidget,
    //   );
    // });

    // testWidgets('renders message when no slots are available', (
    //   WidgetTester tester,
    // ) async {
    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
    //       selectedPitchId: testPitchId1,
    //       pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
    //       availableSlotsValue: const AsyncData([]),
    //     ),
    //   );
    //   await tester.pumpAndSettle();
    //   expect(find.text('No slots available for this day.'), findsOneWidget);
    // });

    // testWidgets('renders available and booked slots correctly', (
    //   WidgetTester tester,
    // ) async {
    //   final slots = createSlots(count: 3, bookedCount: 1, date: testDate);
    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
    //       selectedPitchId: testPitchId1,
    //       pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
    //       availableSlotsValue: AsyncData(slots),
    //     ),
    //   );
    //   await tester.pumpAndSettle();

    //   expect(find.byType(ListTile), findsNWidgets(3));

    //   // Slot 0 (Unavailable - past slot)
    //   final slot0Text =
    //       '${AppDateFormats.shortTime.format(slots[0].startTime.toLocal())} - ${AppDateFormats.shortTime.format(slots[0].endTime.toLocal())}';
    //   expect(find.text(slot0Text), findsOneWidget);
    //   expect(
    //     find.widgetWithText(ElevatedButton, 'Unavailable'),
    //     findsOneWidget,
    //   );
    //   final unavailableButton = tester.widget<ElevatedButton>(
    //     find.widgetWithText(ElevatedButton, 'Unavailable'),
    //   );
    //   expect(unavailableButton.onPressed, isNull);

    //   // Slot 1 (Available)
    //   final slot1Text =
    //       '${AppDateFormats.shortTime.format(slots[1].startTime.toLocal())} - ${AppDateFormats.shortTime.format(slots[1].endTime.toLocal())}';
    //   expect(find.text(slot1Text), findsOneWidget);
    //   final bookButton10AM = find.descendant(
    //     of: find.ancestor(
    //       of: find.text(slot1Text), // Use specific text
    //       matching: find.byType(ListTile),
    //     ),
    //     matching: find.widgetWithText(ElevatedButton, 'Book'),
    //   );
    //   expect(bookButton10AM, findsOneWidget);
    //   expect(
    //     tester.widget<ElevatedButton>(bookButton10AM).onPressed,
    //     isNotNull,
    //   );

    //   // Slot 2 (Available)
    //   final slot2Text =
    //       '${AppDateFormats.shortTime.format(slots[2].startTime.toLocal())} - ${AppDateFormats.shortTime.format(slots[2].endTime.toLocal())}';
    //   expect(find.text(slot2Text), findsOneWidget);
    //   final bookButton11AM = find.descendant(
    //     of: find.ancestor(
    //       of: find.text(slot2Text), // Use specific text
    //       matching: find.byType(ListTile),
    //     ),
    //     matching: find.widgetWithText(ElevatedButton, 'Book'),
    //   );
    //   expect(bookButton11AM, findsOneWidget);
    //   expect(
    //     tester.widget<ElevatedButton>(bookButton11AM).onPressed,
    //     isNotNull,
    //   );

    //   expect(find.widgetWithText(ElevatedButton, 'Book'), findsNWidgets(2));
    // });

    testWidgets('displays slot availability information correctly', (
      WidgetTester tester,
    ) async {
      final slots = createSlots(count: 3, bookedCount: 1, date: testDate);
      final mockBookingService = MockOptimisticBookingService();

      await tester.pumpWidget(
        createTestWidget(
          allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
          selectedPitchId: testPitchId1,
          pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
          availableSlotsValue: AsyncData(slots),
          mockBookingService: mockBookingService,
        ),
      );
      await tester.pumpAndSettle();

      // Check that the pitch name is displayed
      expect(find.textContaining(mockPitchSettings1.name), findsWidgets);

      // Check that slots are displayed - each slot can have multiple ListTiles
      // due to different states (loading, available, booked, etc.)
      final listTiles = find.byType(ListTile);
      expect(listTiles, findsWidgets);

      // Verify dropdown is present and functional
      final dropdown = find.widgetWithText(
        DropdownButtonFormField<int>,
        mockPitchSettings1.name,
      );
      expect(dropdown, findsOneWidget);

      // Check that we have slot time information displayed
      final timePattern = RegExp(r'\d{2}:\d{2}');
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data != null &&
              timePattern.hasMatch(widget.data!),
        ),
        findsWidgets,
      );
    });

    // testWidgets('tapping "Book" button triggers booking creation and shows feedback', (
    //   WidgetTester tester,
    // ) async {
    //   final slots = createSlots(count: 1, bookedCount: 0, date: testDate);
    //   final mockNotifier = MockBookingCreationNotifier();

    //   await tester.pumpWidget(
    //     createTestWidget(
    //       allPitchesAsyncValue: AsyncData(mockPitchSettingsList),
    //       selectedPitchId: testPitchId1,
    //       pitchSettingsAsyncValue: AsyncData(mockPitchSettings1),
    //       availableSlotsValue: AsyncData(slots),
    //       mockNotifier: mockNotifier,
    //       date: testDate, // Ensure testDate is passed here
    //     ),
    //   );
    //   await tester.pumpAndSettle();

    //   // Find the "Book" button for the available slot
    //   final slotText =
    //       '${AppDateFormats.shortTime.format(slots[0].startTime.toLocal())} - ${AppDateFormats.shortTime.format(slots[0].endTime.toLocal())}';
    //   final bookButtonFinder = find.descendant(
    //     of: find.ancestor(
    //       of: find.text(slotText),
    //       matching: find.byType(ListTile),
    //     ),
    //     matching: find.widgetWithText(ElevatedButton, 'Book'),
    //   );
    //   expect(bookButtonFinder, findsOneWidget);

    //   // Tap the "Book" button
    //   await tester.tap(bookButtonFinder);
    //   await tester.pump(); // Start the booking process

    //   // Verify BookingCreationLoading state - UI shows SnackBar feedback
    //   mockNotifier.setTestState(BookingCreationLoading());
    //   await tester.pump(); // Reflect loading state

    //   // Verify BookingCreationSuccess state
    //   // mockNotifier.setTestState(const BookingCreationSuccess('fake_booking_id')); // Old
    //   mockNotifier.setTestState(BookingCreationSuccess()); // New: No bookingId
    //   await tester.pumpAndSettle(); // Let SnackBar appear and settle

    //   // expect(find.text('Booking successful! ID: fake_booking_id'), findsOneWidget); // Old
    //   expect(
    //     find.text('Booking successful!'),
    //     findsOneWidget,
    //   ); // New: Generic message
    //   expect(find.byType(SnackBar), findsOneWidget);

    //   // Verify BookingCreationError state
    //   // mockNotifier.setTestState(const BookingCreationError('Booking failed')); // Old
    //   mockNotifier.setTestState(
    //     BookingCreationError('Booking failed'),
    //   ); // New: No const, message is positional
    //   await tester.pumpAndSettle();
    //   expect(find.text('Booking failed: Booking failed'), findsOneWidget);
    //   expect(
    //     find.byType(SnackBar),
    //     findsOneWidget,
    //   ); // Another SnackBar for error
    // });

    // testWidgets(
    //   'displays error message when availableSlotsProvider returns an error',
    //   (WidgetTester tester) async {
    //     // Arrange
    //     final exception = Exception('Failed to load slots');
    //     // Use a local PitchSettings instance for this test's specific override
    //     final localPitchSettings = PitchSettings(
    //       id: testPitchId1,
    //       name: 'Local Test Pitch',
    //       openTime: '09:00',
    //       closeTime: '17:00',
    //       slotDurationMinutes: 60,
    //       cancellationWindowHours: 2,
    //       createdAt: DateTime.now(),
    //       updatedAt: DateTime.now(),
    //     );

    //     await tester.pumpWidget(
    //       ProviderScope(
    //         overrides: [
    //           ...testProviderOverrides, // Add base overrides
    //           allPitchesProvider.overrideWith(
    //             (ref) => Future.value([localPitchSettings]),
    //           ),
    //           selectedPitchIdProvider.overrideWith((ref) => testPitchId1),
    //           availableSlotsProvider((
    //             date: testDate,
    //             pitchId: testPitchId1,
    //           )).overrideWith((ref) async {
    //             throw exception;
    //           }),
    //           // Provide valid pitch settings so the screen can reach the slots part
    //           pitchSettingsProvider(
    //             testPitchId1,
    //           ).overrideWith((ref) async => localPitchSettings),
    //           userBookingsProvider.overrideWith(
    //             (ref) => Future.value(<Booking>[]),
    //           ), // Add default for userBookings
    //         ],
    //         child: MaterialApp(home: AvailabilityScreen(initialDate: testDate)),
    //       ),
    //     );

    //     // Act
    //     await tester.pumpAndSettle();

    //     // Assert
    //     expect(
    //       find.text('Error fetching available slots: Exception: Failed to load slots'),
    //       findsOneWidget,
    //     );
    //     expect(find.byType(SnackBar), findsOneWidget);
    //   },
    // );

    // testWidgets(
    //   'displays error message when pitchSettingsProvider returns an error',
    //   (WidgetTester tester) async {
    //     // Arrange
    //     final exception = Exception('Failed to load pitch settings');
    //     await tester.pumpWidget(
    //       ProviderScope(
    //         overrides: [
    //           ...testProviderOverrides, // Add base overrides
    //           allPitchesProvider.overrideWith(
    //             (ref) => Future.value([mockPitchSettings1]),
    //           ),
    //           selectedPitchIdProvider.overrideWith((ref) => testPitchId1),
    //           pitchSettingsProvider(testPitchId1).overrideWith((ref) async {
    //             throw exception;
    //           }),
    //           availableSlotsProvider((
    //             date: testDate,
    //             pitchId: testPitchId1,
    //           )).overrideWith((ref) async => <TimeSlotInfo>[]),
    //           userBookingsProvider.overrideWith(
    //             (ref) => Future.value(<Booking>[]),
    //           ), // Add default for userBookings
    //         ],
    //         child: MaterialApp(home: AvailabilityScreen(initialDate: testDate)),
    //       ),
    //     );

    //     // Act
    //     await tester.pumpAndSettle();

    //     // Assert
    //     expect(
    //       find.text(
    //         'Error fetching pitch settings: Exception: Failed to load pitch settings',
    //       ),
    //       findsOneWidget,
    //     );
    //     expect(find.byType(SnackBar), findsOneWidget);
    //   },
    // );
  });
}
