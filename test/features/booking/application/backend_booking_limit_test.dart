import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/booking/application/optimistic_booking_service.dart';
import 'package:skillz/features/booking/domain/booking_exceptions.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/application/real_time_availability_simple.dart'
    as simple_real_time;
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:mocktail/mocktail.dart';

class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockFunctionsClient extends Mock implements FunctionsClient {}

class MockAuthService extends Mock implements GoTrueClient {}

class MockUser extends Mock implements User {}

class MockFunctionResponse extends Mock implements FunctionResponse {}

class MockBookingRepository extends Mock implements BookingRepository {}

class _MockOptimisticBookingState
    extends simple_real_time.SimpleSlotMarkingState {
  @override
  Map<String, bool> build() => <String, bool>{};

  @override
  void markSlotAsOptimisticallyBooked(DateTime startTime, DateTime endTime) {
    // Do nothing in test
  }

  @override
  void clearOptimisticBooking(DateTime startTime, DateTime endTime) {
    // Do nothing in test
  }

  @override
  void clearAllOptimisticBookings() {
    // Do nothing in test
  }
}

void main() {
  group('Backend Booking Limit Enforcement Tests', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockFunctionsClient mockFunctionsClient;
    late MockAuthService mockAuthService;
    late MockUser mockUser;
    late MockBookingRepository mockBookingRepository;
    late ProviderContainer container;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockFunctionsClient = MockFunctionsClient();
      mockAuthService = MockAuthService();
      mockUser = MockUser();
      mockBookingRepository = MockBookingRepository();

      // Setup default mocks
      when(() => mockSupabaseClient.functions).thenReturn(mockFunctionsClient);
      when(() => mockSupabaseClient.auth).thenReturn(mockAuthService);
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
      when(() => mockUser.id).thenReturn('test-user-id');

      container = ProviderContainer(
        overrides: [
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          bookingRepositoryProvider.overrideWithValue(mockBookingRepository),
          simple_real_time.simpleSlotMarkingStateProvider.overrideWith(
            () => _MockOptimisticBookingState(),
          ),
          // Mock providers that would be invalidated to prevent cascading provider calls
          userBookingsProvider.overrideWith((ref) async => []),
          // Override the family providers to return empty lists
          availableSlotsProvider.overrideWith((ref, params) async => []),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test(
      'Handles BOOKING_LIMIT_EXCEEDED error from backend properly',
      () async {
        // Arrange - Mock backend response for booking limit exceeded
        final mockResponse = MockFunctionResponse();
        when(() => mockResponse.status).thenReturn(409);
        when(() => mockResponse.data).thenReturn({
          'error': 'BOOKING_LIMIT_EXCEEDED',
          'message': 'You can only have 4 active bookings at a time',
        });

        when(
          () => mockFunctionsClient.invoke(
            'create_booking_atomic',
            body: any(named: 'body'),
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act - Attempt to create booking
        final service = container.read(
          optimisticBookingServiceProvider.notifier,
        );
        await service.createBookingOptimistic(
          pitchId: 1,
          slotStartTime: DateTime.now().add(const Duration(hours: 1)),
          slotEndTime: DateTime.now().add(const Duration(hours: 2)),
        );

        // Assert - Check that BookingLimitReachedException was properly handled
        final state = service.state;
        expect(state.status, OptimisticBookingStatus.error);
        expect(state.exception, isA<BookingLimitReachedException>());
        expect(state.message, 'You can only have 4 active bookings at a time');
      },
    );

    test('Handles SLOT_UNAVAILABLE error from backend properly', () async {
      // Arrange - Mock backend response for slot unavailable
      final mockResponse = MockFunctionResponse();
      when(() => mockResponse.status).thenReturn(409);
      when(() => mockResponse.data).thenReturn({
        'error': 'SLOT_UNAVAILABLE',
        'message': 'This time slot was just booked by another user',
      });

      when(
        () => mockFunctionsClient.invoke(
          'create_booking_atomic',
          body: any(named: 'body'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act - Attempt to create booking
      final service = container.read(optimisticBookingServiceProvider.notifier);
      await service.createBookingOptimistic(
        pitchId: 1,
        slotStartTime: DateTime.now().add(const Duration(hours: 1)),
        slotEndTime: DateTime.now().add(const Duration(hours: 2)),
      );

      // Assert - Check that SlotUnavailableException was properly handled
      final state = service.state;
      expect(state.status, OptimisticBookingStatus.conflict);
      expect(state.exception, isA<SlotUnavailableException>());
      expect(state.message, 'This time slot was just booked by another user');
    });

    test('Successfully creates booking when under limit', () async {
      // Arrange - Mock successful backend response
      final mockResponse = MockFunctionResponse();
      when(() => mockResponse.status).thenReturn(201);
      when(() => mockResponse.data).thenReturn({
        'success': true,
        'booking': {'id': 123, 'status': 'confirmed'},
        'message': 'Booking created successfully',
      });

      when(
        () => mockFunctionsClient.invoke(
          'create_booking_atomic',
          body: any(named: 'body'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act - Attempt to create booking
      final service = container.read(optimisticBookingServiceProvider.notifier);

      await service.createBookingOptimistic(
        pitchId: 1,
        slotStartTime: DateTime.now().add(const Duration(hours: 1)),
        slotEndTime: DateTime.now().add(const Duration(hours: 2)),
      );

      // Assert - Check that booking was successfully created
      final state = service.state;
      expect(state.status, OptimisticBookingStatus.success);
      expect(state.exception, isNull);
    });
  });
}
