// test/features/booking/application/booking_cancellation_service_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/core/services/time_service.dart';
import 'package:skillz/features/booking/application/booking_cancellation_service.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/core/exceptions/booking_exceptions.dart';

// Test mocks
class MockBookingRepository extends Mock implements BookingRepository {}

class MockTimeService extends Mock implements TimeService {}

void main() {
  group('BookingCancellationService', () {
    late MockBookingRepository mockRepository;
    late MockTimeService mockTimeService;
    late ProviderContainer container;

    // Test data
    final baseTime = DateTime(2025, 7, 9, 14, 30); // 2:30 PM
    final testUser = 'user123';

    Booking createTestBooking({
      int id = 1,
      DateTime? slotStartTime,
      DateTime? slotEndTime,
      BookingStatus status = BookingStatus.confirmed,
    }) {
      final startTime = slotStartTime ?? baseTime.add(const Duration(hours: 6));
      final endTime = slotEndTime ?? startTime.add(const Duration(hours: 1));

      return Booking(
        id: id,
        userId: testUser,
        pitchId: 1,
        slotStartTime: startTime,
        slotEndTime: endTime,
        status: status,
        createdAt: baseTime.subtract(const Duration(days: 1)),
        updatedAt: baseTime.subtract(const Duration(hours: 1)),
      );
    }

    PitchSettings createTestPitchSettings({int cancellationWindowHours = 4}) {
      return PitchSettings(
        id: 1,
        name: 'Test Pitch',
        openTime: '08:00',
        closeTime: '20:00',
        slotDurationMinutes: 90,
        cancellationWindowHours: cancellationWindowHours,
        pricePerHour: 5000.0,
        maxBookingsPerUser: 4,
        isEnabled: true,
        createdAt: baseTime.subtract(const Duration(days: 30)),
        updatedAt: baseTime.subtract(const Duration(days: 1)),
      );
    }

    setUp(() {
      mockRepository = MockBookingRepository();
      mockTimeService = MockTimeService();

      container = ProviderContainer(
        overrides: [
          bookingRepositoryProvider.overrideWithValue(mockRepository),
          timeServiceProvider.overrideWithValue(mockTimeService),
        ],
      );

      // Set up default time service behavior
      when(() => mockTimeService.now()).thenReturn(baseTime);
    });

    tearDown(() {
      container.dispose();
    });

    group('cancelBooking', () {
      test('successfully cancels eligible booking', () async {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 6),
          ), // 6 hours from now
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        when(() => mockRepository.cancelBooking(booking.id)).thenAnswer(
          (_) async => booking.copyWith(status: BookingStatus.cancelledByUser),
        );

        // Act
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        await service.cancelBooking(booking, pitchSettings);

        // Assert
        verify(() => mockRepository.cancelBooking(booking.id)).called(1);
        final state = container.read(bookingCancellationServiceProvider);
        expect(state.isSuccess, isTrue);
        expect(state.message, contains('cancelled successfully'));
      });

      test('throws exception when booking cannot be cancelled', () async {
        // Arrange - booking too close to start time
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 2),
          ), // 2 hours from now
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act & Assert
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        expect(
          () => service.cancelBooking(booking, pitchSettings),
          throwsA(isA<BookingCancellationException>()),
        );

        // Verify repository was not called
        verifyNever(() => mockRepository.cancelBooking(any()));
      });

      test('throws exception when booking is already cancelled', () async {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 6)),
          status: BookingStatus.cancelledByUser,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act & Assert
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        expect(
          () => service.cancelBooking(booking, pitchSettings),
          throwsA(isA<BookingCancellationException>()),
        );

        verifyNever(() => mockRepository.cancelBooking(any()));
      });

      test('handles repository errors gracefully', () async {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 6)),
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        when(
          () => mockRepository.cancelBooking(booking.id),
        ).thenThrow(Exception('Database connection failed'));

        // Act
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        await service.cancelBooking(booking, pitchSettings);

        // Assert
        final state = container.read(bookingCancellationServiceProvider);
        expect(state.isError, isTrue);
        expect(state.message, contains('Failed to cancel booking'));
      });

      test('updates state to loading during cancellation', () async {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 6)),
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Set up delayed response
        when(() => mockRepository.cancelBooking(booking.id)).thenAnswer(
          (_) => Future.delayed(
            const Duration(milliseconds: 100),
            () => booking.copyWith(status: BookingStatus.cancelledByUser),
          ),
        );

        // Act
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        final future = service.cancelBooking(booking, pitchSettings);

        // Assert loading state
        final loadingState = container.read(bookingCancellationServiceProvider);
        expect(loadingState.isLoading, isTrue);

        // Wait for completion
        await future;

        // Assert success state
        final successState = container.read(bookingCancellationServiceProvider);
        expect(successState.isSuccess, isTrue);
      });
    });

    group('deleteBookingFromView', () {
      test('successfully deletes past booking from local view', () async {
        // Arrange - past booking
        final pastBooking = createTestBooking(
          slotStartTime: baseTime.subtract(
            const Duration(hours: 2),
          ), // 2 hours ago
          slotEndTime: baseTime.subtract(
            const Duration(hours: 1),
          ), // 1 hour ago
        );

        when(
          () => mockRepository.deleteBookingFromView(pastBooking.id),
        ).thenAnswer((_) async {});

        // Act
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        await service.deleteBookingFromView(pastBooking);

        // Assert
        verify(
          () => mockRepository.deleteBookingFromView(pastBooking.id),
        ).called(1);
        final state = container.read(bookingCancellationServiceProvider);
        expect(state.isSuccess, isTrue);
        expect(state.message, contains('removed from your view'));
      });

      test('throws exception when trying to delete future booking', () async {
        // Arrange - future booking
        final futureBooking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 2),
          ), // 2 hours from now
        );

        // Act & Assert
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        expect(
          () => service.deleteBookingFromView(futureBooking),
          throwsA(isA<BookingCancellationException>()),
        );

        verifyNever(() => mockRepository.deleteBookingFromView(any()));
      });

      test('throws exception when trying to delete current booking', () async {
        // Arrange - current/active booking
        final currentBooking = createTestBooking(
          slotStartTime: baseTime.subtract(
            const Duration(minutes: 30),
          ), // Started 30 min ago
          slotEndTime: baseTime.add(
            const Duration(minutes: 30),
          ), // Ends in 30 min
        );

        // Act & Assert
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        expect(
          () => service.deleteBookingFromView(currentBooking),
          throwsA(isA<BookingCancellationException>()),
        );

        verifyNever(() => mockRepository.deleteBookingFromView(any()));
      });
    });

    group('state management', () {
      test('resets state correctly', () {
        // Arrange
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );

        // Set state to error
        service.state = const BookingCancellationState(
          status: BookingCancellationStatus.error,
          message: 'Test error',
        );

        // Act
        service.resetState();

        // Assert
        final state = container.read(bookingCancellationServiceProvider);
        expect(state.status, BookingCancellationStatus.idle);
        expect(state.message, isNull);
        expect(state.exception, isNull);
      });

      test('handles multiple rapid cancellation attempts', () async {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 6)),
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        when(() => mockRepository.cancelBooking(booking.id)).thenAnswer(
          (_) => Future.delayed(
            const Duration(milliseconds: 100),
            () => booking.copyWith(status: BookingStatus.cancelledByUser),
          ),
        );

        // Act - start multiple cancellations
        final service = container.read(
          bookingCancellationServiceProvider.notifier,
        );
        final future1 = service.cancelBooking(booking, pitchSettings);
        final future2 = service.cancelBooking(booking, pitchSettings);

        // Assert - second call should be ignored (already processing)
        await Future.wait([future1, future2]);

        // Repository should only be called once
        verify(() => mockRepository.cancelBooking(booking.id)).called(1);
      });
    });

    group('integration with pitch settings', () {
      test('respects different cancellation windows from backend', () async {
        // Test data for different cancellation windows
        final testCases = [
          {
            'windowHours': 1,
            'shouldSucceed': true,
            'description': '6 hours > 1 hour window',
          },
          {
            'windowHours': 8,
            'shouldSucceed': false,
            'description': '6 hours < 8 hour window',
          },
          {
            'windowHours': 24,
            'shouldSucceed': false,
            'description': '6 hours < 24 hour window',
          },
        ];

        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 6),
          ), // 6 hours from now
        );

        for (final testCase in testCases) {
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: testCase['windowHours'] as int,
          );

          final service = container.read(
            bookingCancellationServiceProvider.notifier,
          );

          if (testCase['shouldSucceed'] as bool) {
            // Should succeed - set up mock
            when(() => mockRepository.cancelBooking(booking.id)).thenAnswer(
              (_) async =>
                  booking.copyWith(status: BookingStatus.cancelledByUser),
            );

            await service.cancelBooking(booking, pitchSettings);
            final state = container.read(bookingCancellationServiceProvider);
            expect(
              state.isSuccess,
              isTrue,
              reason: testCase['description'] as String,
            );
          } else {
            // Should fail with exception
            expect(
              () => service.cancelBooking(booking, pitchSettings),
              throwsA(isA<BookingCancellationException>()),
              reason: testCase['description'] as String,
            );
          }

          // Reset state for next test
          service.resetState();
          reset(mockRepository);
        }
      });
    });
  });
}
