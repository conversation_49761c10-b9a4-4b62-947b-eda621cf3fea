// test/features/booking/domain/booking_business_rules_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/booking/domain/booking_business_rules.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';

void main() {
  group('BookingBusinessRules', () {
    // Test data setup
    final baseTime = DateTime(2025, 7, 9, 14, 30); // 2:30 PM

    Booking createTestBooking({
      DateTime? slotStartTime,
      DateTime? slotEndTime,
      BookingStatus status = BookingStatus.confirmed,
    }) {
      return Booking(
        id: 1,
        userId: 'user123',
        pitchId: 1,
        slotStartTime: slotStartTime ?? baseTime,
        slotEndTime: slotEndTime ?? baseTime.add(const Duration(hours: 1)),
        status: status,
        createdAt: baseTime.subtract(const Duration(days: 1)),
        updatedAt: baseTime.subtract(const Duration(hours: 1)),
      );
    }

    group('isUpcomingBooking', () {
      test('returns true when booking starts after current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 1)),
        );

        expect(
          BookingBusinessRules.isUpcomingBooking(booking, baseTime),
          isTrue,
        );
      });

      test('returns false when booking starts before current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 1)),
        );

        expect(
          BookingBusinessRules.isUpcomingBooking(booking, baseTime),
          isFalse,
        );
      });

      test('returns false when booking starts exactly at current time', () {
        final booking = createTestBooking(slotStartTime: baseTime);

        expect(
          BookingBusinessRules.isUpcomingBooking(booking, baseTime),
          isFalse,
        );
      });

      test('handles edge case with millisecond precision', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(milliseconds: 1)),
        );

        expect(
          BookingBusinessRules.isUpcomingBooking(booking, baseTime),
          isTrue,
        );
      });
    });

    group('isPastBooking', () {
      test('returns true when booking ends before current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 2)),
          slotEndTime: baseTime.subtract(const Duration(hours: 1)),
        );

        expect(BookingBusinessRules.isPastBooking(booking, baseTime), isTrue);
      });

      test('returns false when booking ends after current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 1)),
          slotEndTime: baseTime.add(const Duration(hours: 1)),
        );

        expect(BookingBusinessRules.isPastBooking(booking, baseTime), isFalse);
      });

      test('returns false when booking ends exactly at current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 1)),
          slotEndTime: baseTime,
        );

        expect(BookingBusinessRules.isPastBooking(booking, baseTime), isFalse);
      });

      test('BUG FIX: booking 10:00-11:00 at current time 11:19', () {
        // Test the original reported bug scenario
        final currentTime = DateTime(2025, 7, 9, 11, 19); // 11:19
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 10, 0), // 10:00
          slotEndTime: DateTime(2025, 7, 9, 11, 0), // 11:00
        );

        // Fixed logic: uses END time for classification
        // End time (11:00) < current time (11:19) = PAST ✓
        expect(
          BookingBusinessRules.isPastBooking(booking, currentTime),
          isTrue,
          reason: 'Fixed logic: end time 11:00 < current time 11:19 = past',
        );

        expect(
          BookingBusinessRules.isUpcomingBooking(booking, currentTime),
          isFalse,
          reason:
              'Fixed logic: start time 10:00 < current time 11:19 = not upcoming',
        );
      });

      test(
        'BUG FIX: booking 10:30-11:30 at current time 11:19 should be current',
        () {
          // Test a scenario where the booking is still active
          final currentTime = DateTime(2025, 7, 9, 11, 19); // 11:19
          final booking = createTestBooking(
            slotStartTime: DateTime(2025, 7, 9, 10, 30), // 10:30
            slotEndTime: DateTime(2025, 7, 9, 11, 30), // 11:30
          );

          // Fixed logic: end time (11:30) > current time (11:19) = NOT PAST
          // This booking should be considered CURRENT, not past
          expect(
            BookingBusinessRules.isPastBooking(booking, currentTime),
            isFalse,
            reason:
                'Fixed logic: end time 11:30 > current time 11:19 = not past (still active)',
          );

          expect(
            BookingBusinessRules.isUpcomingBooking(booking, currentTime),
            isFalse,
            reason:
                'Fixed logic: start time 10:30 < current time 11:19 = not upcoming',
          );

          expect(
            BookingBusinessRules.isCurrentBooking(booking, currentTime),
            isTrue,
            reason: 'Fixed logic: 10:30 <= 11:19 < 11:30 = current/active',
          );
        },
      );
    });

    group('isCurrentBooking', () {
      test('returns true when current time is between start and end time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(minutes: 30)),
          slotEndTime: baseTime.add(const Duration(minutes: 30)),
        );

        expect(
          BookingBusinessRules.isCurrentBooking(booking, baseTime),
          isTrue,
        );
      });

      test('returns true when booking starts exactly at current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime,
          slotEndTime: baseTime.add(const Duration(hours: 1)),
        );

        expect(
          BookingBusinessRules.isCurrentBooking(booking, baseTime),
          isTrue,
        );
      });

      test('returns false when booking ends exactly at current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 1)),
          slotEndTime: baseTime,
        );

        expect(
          BookingBusinessRules.isCurrentBooking(booking, baseTime),
          isFalse,
        );
      });

      test('returns false when booking is completely in the past', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 2)),
          slotEndTime: baseTime.subtract(const Duration(hours: 1)),
        );

        expect(
          BookingBusinessRules.isCurrentBooking(booking, baseTime),
          isFalse,
        );
      });

      test('returns false when booking is completely in the future', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.add(const Duration(hours: 1)),
          slotEndTime: baseTime.add(const Duration(hours: 2)),
        );

        expect(
          BookingBusinessRules.isCurrentBooking(booking, baseTime),
          isFalse,
        );
      });
    });

    group('countsTowardBookingLimit', () {
      test('returns true for confirmed booking with future end time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(minutes: 30)),
          slotEndTime: baseTime.add(const Duration(minutes: 30)),
          status: BookingStatus.confirmed,
        );

        expect(
          BookingBusinessRules.countsTowardBookingLimit(booking, baseTime),
          isTrue,
        );
      });

      test('returns true for pending payment booking with future end time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(minutes: 30)),
          slotEndTime: baseTime.add(const Duration(minutes: 30)),
          status: BookingStatus.pendingPayment,
        );

        expect(
          BookingBusinessRules.countsTowardBookingLimit(booking, baseTime),
          isTrue,
        );
      });

      test('returns false for cancelled booking even with future end time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(minutes: 30)),
          slotEndTime: baseTime.add(const Duration(minutes: 30)),
          status: BookingStatus.cancelledByUser,
        );

        expect(
          BookingBusinessRules.countsTowardBookingLimit(booking, baseTime),
          isFalse,
        );
      });

      test('returns false for booking with past end time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 2)),
          slotEndTime: baseTime.subtract(const Duration(hours: 1)),
          status: BookingStatus.confirmed,
        );

        expect(
          BookingBusinessRules.countsTowardBookingLimit(booking, baseTime),
          isFalse,
        );
      });

      test('returns false when end time equals current time', () {
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(const Duration(hours: 1)),
          slotEndTime: baseTime,
          status: BookingStatus.confirmed,
        );

        expect(
          BookingBusinessRules.countsTowardBookingLimit(booking, baseTime),
          isFalse,
        );
      });
    });

    group('isCancelledBooking', () {
      test('returns true for cancelledByUser status', () {
        final booking = createTestBooking(
          status: BookingStatus.cancelledByUser,
        );

        expect(BookingBusinessRules.isCancelledBooking(booking), isTrue);
      });

      test('returns true for cancelledByAdmin status', () {
        final booking = createTestBooking(
          status: BookingStatus.cancelledByAdmin,
        );

        expect(BookingBusinessRules.isCancelledBooking(booking), isTrue);
      });

      test('returns false for confirmed status', () {
        final booking = createTestBooking(status: BookingStatus.confirmed);

        expect(BookingBusinessRules.isCancelledBooking(booking), isFalse);
      });

      test('returns false for other statuses', () {
        final statuses = [
          BookingStatus.pendingPayment,
          BookingStatus.completed,
          BookingStatus.noShow,
        ];

        for (final status in statuses) {
          final booking = createTestBooking(status: status);
          expect(
            BookingBusinessRules.isCancelledBooking(booking),
            isFalse,
            reason: 'Should return false for status: $status',
          );
        }
      });
    });

    group('isActiveBooking', () {
      test('returns true for confirmed status', () {
        final booking = createTestBooking(status: BookingStatus.confirmed);

        expect(BookingBusinessRules.isActiveBooking(booking), isTrue);
      });

      test('returns true for pendingPayment status', () {
        final booking = createTestBooking(status: BookingStatus.pendingPayment);

        expect(BookingBusinessRules.isActiveBooking(booking), isTrue);
      });

      test('returns false for cancelled statuses', () {
        final cancelledStatuses = [
          BookingStatus.cancelledByUser,
          BookingStatus.cancelledByAdmin,
        ];

        for (final status in cancelledStatuses) {
          final booking = createTestBooking(status: status);
          expect(
            BookingBusinessRules.isActiveBooking(booking),
            isFalse,
            reason: 'Should return false for status: $status',
          );
        }
      });

      test('returns false for completed and noShow statuses', () {
        final inactiveStatuses = [
          BookingStatus.completed,
          BookingStatus.noShow,
        ];

        for (final status in inactiveStatuses) {
          final booking = createTestBooking(status: status);
          expect(
            BookingBusinessRules.isActiveBooking(booking),
            isFalse,
            reason: 'Should return false for status: $status',
          );
        }
      });
    });

    group('isSlotBookable', () {
      test('returns true for future slot', () {
        final slotStartTime = baseTime.add(const Duration(hours: 1));

        expect(
          BookingBusinessRules.isSlotBookable(slotStartTime, baseTime),
          isTrue,
        );
      });

      test('returns true for slot within grace period', () {
        final slotStartTime = baseTime.subtract(const Duration(minutes: 3));

        expect(
          BookingBusinessRules.isSlotBookable(slotStartTime, baseTime),
          isTrue,
        );
      });

      test('returns false for slot beyond grace period', () {
        final slotStartTime = baseTime.subtract(const Duration(minutes: 10));

        expect(
          BookingBusinessRules.isSlotBookable(slotStartTime, baseTime),
          isFalse,
        );
      });

      test('returns true for slot exactly at grace period boundary', () {
        final slotStartTime = baseTime.subtract(const Duration(minutes: 5));

        expect(
          BookingBusinessRules.isSlotBookable(slotStartTime, baseTime),
          isFalse,
        );
      });

      test('respects custom grace period', () {
        final slotStartTime = baseTime.subtract(const Duration(minutes: 8));

        expect(
          BookingBusinessRules.isSlotBookable(
            slotStartTime,
            baseTime,
            gracePeriod: const Duration(minutes: 10),
          ),
          isTrue,
        );
      });
    });

    group('collection methods', () {
      late List<Booking> testBookings;

      setUp(() {
        testBookings = [
          // Past booking
          createTestBooking(
            slotStartTime: baseTime.subtract(const Duration(hours: 2)),
            slotEndTime: baseTime.subtract(const Duration(hours: 1)),
          ),
          // Current booking (starts exactly at baseTime)
          createTestBooking(
            slotStartTime: baseTime,
            slotEndTime: baseTime.add(const Duration(hours: 1)),
          ),
          // Future booking
          createTestBooking(
            slotStartTime: baseTime.add(const Duration(hours: 1)),
            slotEndTime: baseTime.add(const Duration(hours: 2)),
          ),
          // Cancelled future booking
          createTestBooking(
            slotStartTime: baseTime.add(const Duration(hours: 2)),
            slotEndTime: baseTime.add(const Duration(hours: 3)),
            status: BookingStatus.cancelledByUser,
          ),
        ];
      });

      test('filterUpcomingBookings returns only future bookings', () {
        final upcoming = BookingBusinessRules.filterUpcomingBookings(
          testBookings,
          baseTime,
        );

        expect(upcoming, hasLength(2));
        expect(
          upcoming[0].slotStartTime,
          baseTime.add(const Duration(hours: 1)),
        );
        expect(
          upcoming[1].slotStartTime,
          baseTime.add(const Duration(hours: 2)),
        );
      });

      test('filterPastBookings returns only past bookings', () {
        final past = BookingBusinessRules.filterPastBookings(
          testBookings,
          baseTime,
        );

        expect(past, hasLength(1));
        expect(
          past[0].slotStartTime,
          baseTime.subtract(const Duration(hours: 2)),
        );
      });

      test(
        'countActiveBookings counts only active bookings with future end times',
        () {
          final count = BookingBusinessRules.countActiveBookings(
            testBookings,
            baseTime,
          );

          // Should count: current booking (ends in future) + future confirmed booking
          // Should NOT count: past booking (ended) + cancelled booking
          expect(count, equals(2));
        },
      );

      test('handles empty list gracefully', () {
        expect(
          BookingBusinessRules.filterUpcomingBookings([], baseTime),
          isEmpty,
        );
        expect(BookingBusinessRules.filterPastBookings([], baseTime), isEmpty);
        expect(
          BookingBusinessRules.countActiveBookings([], baseTime),
          equals(0),
        );
      });
    });
  });
}
