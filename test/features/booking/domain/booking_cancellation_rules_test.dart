// test/features/booking/domain/booking_cancellation_rules_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/booking/domain/booking_cancellation_rules.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';

void main() {
  group('BookingCancellationRules', () {
    // Test data setup
    final baseTime = DateTime(2025, 7, 9, 14, 30); // 2:30 PM

    Booking createTestBooking({
      DateTime? slotStartTime,
      DateTime? slotEndTime,
      BookingStatus status = BookingStatus.confirmed,
      int id = 1,
    }) {
      final startTime = slotStartTime ?? baseTime.add(const Duration(hours: 2));
      final endTime = slotEndTime ?? startTime.add(const Duration(hours: 1));

      return Booking(
        id: id,
        userId: 'user123',
        pitchId: 1,
        slotStartTime: startTime,
        slotEndTime: endTime,
        status: status,
        createdAt: baseTime.subtract(const Duration(days: 1)),
        updatedAt: baseTime.subtract(const Duration(hours: 1)),
      );
    }

    PitchSettings createTestPitchSettings({int cancellationWindowHours = 4}) {
      return PitchSettings(
        id: 1,
        name: 'Test Pitch',
        openTime: '08:00',
        closeTime: '20:00',
        slotDurationMinutes: 90,
        cancellationWindowHours: cancellationWindowHours,
        pricePerHour: 5000.0,
        maxBookingsPerUser: 4,
        isEnabled: true,
        createdAt: baseTime.subtract(const Duration(days: 30)),
        updatedAt: baseTime.subtract(const Duration(days: 1)),
      );
    }

    group('canCancelBooking', () {
      test(
        'allows cancellation within cancellation window for confirmed booking',
        () {
          // Arrange
          final booking = createTestBooking(
            slotStartTime: baseTime.add(
              const Duration(hours: 6),
            ), // 6 hours from now
            status: BookingStatus.confirmed,
          );
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: 4,
          );

          // Act
          final canCancel = BookingCancellationRules.canCancelBooking(
            booking,
            pitchSettings,
            baseTime,
          );

          // Assert
          expect(
            canCancel,
            isTrue,
            reason: '6 hours > 4 hour cancellation window',
          );
        },
      );

      test(
        'allows cancellation within cancellation window for pending payment booking',
        () {
          // Arrange
          final booking = createTestBooking(
            slotStartTime: baseTime.add(
              const Duration(hours: 5),
            ), // 5 hours from now
            status: BookingStatus.pendingPayment,
          );
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: 4,
          );

          // Act
          final canCancel = BookingCancellationRules.canCancelBooking(
            booking,
            pitchSettings,
            baseTime,
          );

          // Assert
          expect(
            canCancel,
            isTrue,
            reason: '5 hours > 4 hour cancellation window',
          );
        },
      );

      test('denies cancellation outside cancellation window', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 3),
          ), // 3 hours from now
          status: BookingStatus.confirmed,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(
          canCancel,
          isFalse,
          reason: '3 hours < 4 hour cancellation window',
        );
      });

      test('denies cancellation exactly at cancellation window boundary', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 4),
          ), // Exactly 4 hours from now
          status: BookingStatus.confirmed,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(
          canCancel,
          isFalse,
          reason: 'Exactly at deadline should not allow cancellation',
        );
      });

      test('denies cancellation for already cancelled booking', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 6),
          ), // 6 hours from now
          status: BookingStatus.cancelledByUser,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(
          canCancel,
          isFalse,
          reason: 'Cannot cancel already cancelled booking',
        );
      });

      test('denies cancellation for completed booking', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 6),
          ), // 6 hours from now
          status: BookingStatus.completed,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(canCancel, isFalse, reason: 'Cannot cancel completed booking');
      });

      test('denies cancellation for no-show booking', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 6),
          ), // 6 hours from now
          status: BookingStatus.noShow,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(canCancel, isFalse, reason: 'Cannot cancel no-show booking');
      });

      test('denies cancellation for past booking even within window', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.subtract(
            const Duration(hours: 1),
          ), // 1 hour ago
          status: BookingStatus.confirmed,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(canCancel, isFalse, reason: 'Cannot cancel past booking');
      });

      test('handles different cancellation window configurations', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 2),
          ), // 2 hours from now
          status: BookingStatus.confirmed,
        );

        // Test different cancellation windows
        final testCases = [
          {
            'windowHours': 1,
            'expectedResult': true,
            'reason': '2 hours > 1 hour window',
          },
          {
            'windowHours': 2,
            'expectedResult': false,
            'reason': '2 hours = 2 hour window (boundary)',
          },
          {
            'windowHours': 3,
            'expectedResult': false,
            'reason': '2 hours < 3 hour window',
          },
          {
            'windowHours': 24,
            'expectedResult': false,
            'reason': '2 hours < 24 hour window',
          },
        ];

        for (final testCase in testCases) {
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: testCase['windowHours'] as int,
          );

          // Act
          final canCancel = BookingCancellationRules.canCancelBooking(
            booking,
            pitchSettings,
            baseTime,
          );

          // Assert
          expect(
            canCancel,
            testCase['expectedResult'],
            reason: testCase['reason'] as String,
          );
        }
      });

      test('handles edge case with millisecond precision', () {
        // Arrange - booking starts 4 hours and 1 millisecond from now
        final booking = createTestBooking(
          slotStartTime: baseTime.add(
            const Duration(hours: 4, milliseconds: 1),
          ),
          status: BookingStatus.confirmed,
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final canCancel = BookingCancellationRules.canCancelBooking(
          booking,
          pitchSettings,
          baseTime,
        );

        // Assert
        expect(
          canCancel,
          isTrue,
          reason: 'Should allow cancellation with 1ms buffer',
        );
      });
    });

    group('getCancellationDeadline', () {
      test('calculates correct deadline for 4-hour window', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 18, 0), // 6:00 PM
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final deadline = BookingCancellationRules.getCancellationDeadline(
          booking,
          pitchSettings,
        );

        // Assert
        expect(deadline, equals(DateTime(2025, 7, 9, 14, 0))); // 2:00 PM
      });

      test('calculates correct deadline for different window sizes', () {
        // Arrange
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 18, 0), // 6:00 PM
        );

        final testCases = [
          {
            'windowHours': 1,
            'expectedDeadline': DateTime(2025, 7, 9, 17, 0),
          }, // 5:00 PM
          {
            'windowHours': 6,
            'expectedDeadline': DateTime(2025, 7, 9, 12, 0),
          }, // 12:00 PM
          {
            'windowHours': 24,
            'expectedDeadline': DateTime(2025, 7, 8, 18, 0),
          }, // Previous day 6:00 PM
        ];

        for (final testCase in testCases) {
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: testCase['windowHours'] as int,
          );

          // Act
          final deadline = BookingCancellationRules.getCancellationDeadline(
            booking,
            pitchSettings,
          );

          // Assert
          expect(deadline, equals(testCase['expectedDeadline']));
        }
      });

      test(
        'handles zero cancellation window (immediate cancellation deadline)',
        () {
          // Arrange
          final booking = createTestBooking(
            slotStartTime: DateTime(2025, 7, 9, 18, 0), // 6:00 PM
          );
          final pitchSettings = createTestPitchSettings(
            cancellationWindowHours: 0,
          );

          // Act
          final deadline = BookingCancellationRules.getCancellationDeadline(
            booking,
            pitchSettings,
          );

          // Assert
          expect(
            deadline,
            equals(DateTime(2025, 7, 9, 18, 0)),
          ); // Same as start time
        },
      );
    });

    group('isValidStatusForCancellation', () {
      test('returns true for confirmed status', () {
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.confirmed,
          ),
          isTrue,
        );
      });

      test('returns true for pending payment status', () {
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.pendingPayment,
          ),
          isTrue,
        );
      });

      test('returns false for cancelled statuses', () {
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.cancelledByUser,
          ),
          isFalse,
        );
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.cancelledByAdmin,
          ),
          isFalse,
        );
      });

      test('returns false for completed status', () {
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.completed,
          ),
          isFalse,
        );
      });

      test('returns false for no-show status', () {
        expect(
          BookingCancellationRules.isValidStatusForCancellation(
            BookingStatus.noShow,
          ),
          isFalse,
        );
      });
    });

    group('getCancellationTimeRemaining', () {
      test('returns correct time remaining before cancellation deadline', () {
        // Arrange
        final currentTime = DateTime(2025, 7, 9, 14, 0); // 2:00 PM
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 20, 0), // 8:00 PM
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final timeRemaining =
            BookingCancellationRules.getCancellationTimeRemaining(
              booking,
              pitchSettings,
              currentTime,
            );

        // Assert
        // Cancellation deadline is 8:00 PM - 4 hours = 4:00 PM
        // Time remaining is 4:00 PM - 2:00 PM = 2 hours
        expect(timeRemaining, equals(const Duration(hours: 2)));
      });

      test('returns zero duration when past cancellation deadline', () {
        // Arrange
        final currentTime = DateTime(2025, 7, 9, 17, 0); // 5:00 PM
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 20, 0), // 8:00 PM
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final timeRemaining =
            BookingCancellationRules.getCancellationTimeRemaining(
              booking,
              pitchSettings,
              currentTime,
            );

        // Assert
        // Cancellation deadline is 8:00 PM - 4 hours = 4:00 PM
        // Current time 5:00 PM > deadline 4:00 PM = past deadline
        expect(timeRemaining, equals(Duration.zero));
      });

      test('handles negative duration gracefully', () {
        // Arrange
        final currentTime = DateTime(2025, 7, 9, 19, 0); // 7:00 PM
        final booking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 20, 0), // 8:00 PM
        );
        final pitchSettings = createTestPitchSettings(
          cancellationWindowHours: 4,
        );

        // Act
        final timeRemaining =
            BookingCancellationRules.getCancellationTimeRemaining(
              booking,
              pitchSettings,
              currentTime,
            );

        // Assert
        // Should return zero instead of negative duration
        expect(timeRemaining, equals(Duration.zero));
      });
    });

    group('canDeleteBookingFromView', () {
      test('allows deletion of past bookings regardless of status', () {
        final currentTime = DateTime(2025, 7, 9, 14, 0); // 2:00 PM

        final testStatuses = [
          BookingStatus.confirmed,
          BookingStatus.cancelledByUser,
          BookingStatus.completed,
          BookingStatus.noShow,
        ];

        for (final status in testStatuses) {
          final pastBooking = createTestBooking(
            slotStartTime: currentTime.subtract(
              const Duration(hours: 2),
            ), // 12:00 PM (past)
            status: status,
          );

          final canDelete = BookingCancellationRules.canDeleteBookingFromView(
            pastBooking,
            currentTime,
          );

          expect(
            canDelete,
            isTrue,
            reason: 'Should allow deletion of past booking with status $status',
          );
        }
      });

      test('denies deletion of future bookings', () {
        final currentTime = DateTime(2025, 7, 9, 14, 0); // 2:00 PM

        final futureBooking = createTestBooking(
          slotStartTime: currentTime.add(
            const Duration(hours: 2),
          ), // 4:00 PM (future)
          status: BookingStatus.confirmed,
        );

        final canDelete = BookingCancellationRules.canDeleteBookingFromView(
          futureBooking,
          currentTime,
        );

        expect(
          canDelete,
          isFalse,
          reason: 'Should not allow deletion of future booking',
        );
      });

      test('denies deletion of current/active bookings', () {
        final currentTime = DateTime(2025, 7, 9, 14, 30); // 2:30 PM

        final currentBooking = createTestBooking(
          slotStartTime: DateTime(2025, 7, 9, 14, 0), // 2:00 PM (started)
          slotEndTime: DateTime(2025, 7, 9, 15, 0), // 3:00 PM (not ended)
          status: BookingStatus.confirmed,
        );

        final canDelete = BookingCancellationRules.canDeleteBookingFromView(
          currentBooking,
          currentTime,
        );

        expect(
          canDelete,
          isFalse,
          reason: 'Should not allow deletion of active booking',
        );
      });
    });
  });
}
