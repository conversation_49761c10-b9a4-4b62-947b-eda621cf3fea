// test/test_helpers/infrastructure_validation_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'database_test_helper.dart';
import 'test_time_service.dart';
import 'test_data_factory.dart';

/// Simple integration test to validate our test infrastructure works correctly
void main() {
  group('Test Infrastructure Validation', () {
    setUp(() async {
      await DatabaseTestHelper.setupTestDatabase();
    });

    tearDown(() async {
      await DatabaseTestHelper.cleanupTestData();
    });

    test('TestTimeService provides controlled time', () {
      final fixedTime = DateTime(2025, 7, 9, 14, 30);
      final testTimeService = TestTimeService(fixedTime);
      
      expect(testTimeService.now(), equals(fixedTime));
      
      // Verify it always returns the same time
      expect(testTimeService.now(), equals(fixedTime));
    });

    test('TestTimeScenarios provide consistent test times', () {
      final baseTime = TestTimeScenarios.baseTime;
      final tomorrowAt9AM = TestTimeScenarios.tomorrowAt9AM;
      
      expect(baseTime.year, equals(2025));
      expect(baseTime.month, equals(7));
      expect(baseTime.day, equals(9));
      expect(baseTime.hour, equals(14));
      expect(baseTime.minute, equals(30));
      
      expect(tomorrowAt9AM.day, equals(10));
      expect(tomorrowAt9AM.hour, equals(9));
    });

    test('TestDataFactory creates realistic booking data', () async {
      final bookings = await TestDataFactory.createTestBookings(
        count: 3,
        userId: 'test-user',
        baseTime: TestTimeScenarios.baseTime,
      );
      
      expect(bookings.length, equals(3));
      expect(bookings.first.userId, equals('test-user'));
      expect(bookings.first.status, equals(BookingStatus.confirmed));
      
      // Verify bookings are spaced 1 hour apart
      expect(
        bookings[1].slotStartTime.difference(bookings[0].slotStartTime),
        equals(const Duration(hours: 1)),
      );
    });

    test('TestDataFactory creates maximum active bookings scenario', () async {
      final bookings = await TestDataFactory.createMaximumActiveBookings(
        userId: 'test-user',
        maxBookings: 4,
      );
      
      expect(bookings.length, equals(4));
      expect(bookings.every((b) => b.status == BookingStatus.confirmed), isTrue);
      expect(bookings.every((b) => b.userId == 'test-user'), isTrue);
    });

    test('TestDataFactory creates mixed status bookings', () async {
      final bookings = await TestDataFactory.createMixedStatusBookings(
        userId: 'test-user',
      );
      
      expect(bookings.length, equals(4));
      
      // Should have confirmed and cancelled bookings
      final confirmedBookings = bookings.where((b) => b.status == BookingStatus.confirmed);
      final cancelledBookings = bookings.where((b) => b.status == BookingStatus.cancelledByUser);
      
      expect(confirmedBookings.length, equals(3));
      expect(cancelledBookings.length, equals(1));
    });

    test('TestDataFactory creates time slots', () async {
      final date = DateTime(2025, 7, 10);
      final slots = await TestDataFactory.createTestTimeSlots(
        pitchId: 1,
        date: date,
        slotCount: 8,
      );
      
      expect(slots.length, equals(8));
      expect(slots.first.startTime.hour, equals(9)); // 9 AM start
      expect(slots.last.startTime.hour, equals(16)); // 4 PM (8th slot)
      
      // Verify all slots are initially not booked
      expect(slots.every((s) => !s.isBooked), isTrue);
    });

    test('DatabaseTestHelper can be initialized and cleaned up', () async {
      // This test verifies the database helper doesn't crash
      await DatabaseTestHelper.setupTestDatabase();
      
      // Verify we can get the client
      final client = DatabaseTestHelper.testClient;
      expect(client, isNotNull);
      
      // Cleanup should not throw
      await DatabaseTestHelper.cleanupTestData();
      
      DatabaseTestHelper.dispose();
    });
  });
}
