// test/test_helpers/test_data_factory.dart

import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'database_test_helper.dart';
import 'test_time_service.dart';

/// Factory for creating test data with realistic business scenarios
/// 
/// This factory provides methods to create test data that reflects real
/// business scenarios while maintaining test isolation and predictability.
class TestDataFactory {
  
  /// Create test bookings for various business scenarios
  static Future<List<Booking>> createTestBookings({
    required int count,
    required String userId,
    DateTime? baseTime,
    int pitchId = 1,
    BookingStatus status = BookingStatus.confirmed,
    bool useDatabase = false,
  }) async {
    if (useDatabase) {
      return DatabaseTestHelper.createTestBookings(
        count: count,
        userId: userId,
        baseTime: baseTime,
        pitchId: pitchId,
        status: status,
      );
    }
    
    // Create in-memory test bookings
    final time = baseTime ?? TestTimeScenarios.baseTime;
    final bookings = <Booking>[];
    
    for (int i = 0; i < count; i++) {
      final startTime = time.add(Duration(hours: i));
      final endTime = startTime.add(const Duration(hours: 1));
      
      final booking = Booking(
        id: DateTime.now().millisecondsSinceEpoch + i,
        userId: userId,
        pitchId: pitchId,
        slotStartTime: startTime,
        slotEndTime: endTime,
        status: status,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      bookings.add(booking);
    }
    
    return bookings;
  }
  
  /// Create maximum active bookings (at the limit)
  static Future<List<Booking>> createMaximumActiveBookings({
    String userId = 'test-user',
    int maxBookings = 4,
    DateTime? baseTime,
    bool useDatabase = false,
  }) async {
    final time = baseTime ?? TestTimeScenarios.oneHourFromNow;
    
    return createTestBookings(
      count: maxBookings,
      userId: userId,
      baseTime: time,
      status: BookingStatus.confirmed,
      useDatabase: useDatabase,
    );
  }
  
  /// Create bookings under the limit
  static Future<List<Booking>> createUnderLimitBookings({
    String userId = 'test-user',
    int bookingCount = 3,
    DateTime? baseTime,
    bool useDatabase = false,
  }) async {
    final time = baseTime ?? TestTimeScenarios.oneHourFromNow;
    
    return createTestBookings(
      count: bookingCount,
      userId: userId,
      baseTime: time,
      status: BookingStatus.confirmed,
      useDatabase: useDatabase,
    );
  }
  
  /// Create mixed status bookings for comprehensive testing
  static Future<List<Booking>> createMixedStatusBookings({
    String userId = 'test-user',
    DateTime? baseTime,
    bool useDatabase = false,
  }) async {
    final time = baseTime ?? TestTimeScenarios.baseTime;
    final bookings = <Booking>[];
    
    // Past confirmed booking (should not count toward limit)
    bookings.addAll(await createTestBookings(
      count: 1,
      userId: userId,
      baseTime: time.subtract(const Duration(hours: 2)),
      status: BookingStatus.confirmed,
      useDatabase: useDatabase,
    ));
    
    // Current confirmed booking (should count toward limit)
    bookings.addAll(await createTestBookings(
      count: 1,
      userId: userId,
      baseTime: time.subtract(const Duration(minutes: 30)),
      status: BookingStatus.confirmed,
      useDatabase: useDatabase,
    ));
    
    // Future confirmed booking (should count toward limit)
    bookings.addAll(await createTestBookings(
      count: 1,
      userId: userId,
      baseTime: time.add(const Duration(hours: 1)),
      status: BookingStatus.confirmed,
      useDatabase: useDatabase,
    ));
    
    // Cancelled booking (should not count toward limit)
    bookings.addAll(await createTestBookings(
      count: 1,
      userId: userId,
      baseTime: time.add(const Duration(hours: 2)),
      status: BookingStatus.cancelled,
      useDatabase: useDatabase,
    ));
    
    return bookings;
  }
  
  /// Create test pitch settings with various configurations
  static Future<PitchSettings> createTestPitchSettings({
    int id = 1,
    String name = 'Test Pitch',
    int maxBookingsPerUser = 4,
    double pricePerHour = 5000.0,
    bool isEnabled = true,
    bool useDatabase = false,
  }) async {
    if (useDatabase) {
      return DatabaseTestHelper.createTestPitch(
        id: id,
        name: name,
        maxBookings: maxBookingsPerUser,
        pricePerHour: pricePerHour,
      );
    }
    
    return PitchSettings(
      id: id,
      name: name,
      openTime: '09:00',
      closeTime: '22:00',
      slotDurationMinutes: 60,
      cancellationWindowHours: 24,
      pricePerHour: pricePerHour,
      maxBookingsPerUser: maxBookingsPerUser,
      isEnabled: isEnabled,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
  
  /// Create test time slots for availability testing
  static Future<List<TimeSlotInfo>> createTestTimeSlots({
    required int pitchId,
    required DateTime date,
    int slotCount = 8,
    bool useDatabase = false,
  }) async {
    if (useDatabase) {
      return DatabaseTestHelper.createTestSlots(
        pitchId: pitchId,
        date: date,
        slotCount: slotCount,
      );
    }
    
    final slots = <TimeSlotInfo>[];
    final baseTime = DateTime(date.year, date.month, date.day, 9, 0); // 9 AM start
    
    for (int i = 0; i < slotCount; i++) {
      final startTime = baseTime.add(Duration(hours: i));
      final endTime = startTime.add(const Duration(hours: 1));
      
      final slot = TimeSlotInfo(
        startTime: startTime,
        endTime: endTime,
        isAvailable: true,
        isBooked: false,
        pitchId: pitchId,
      );
      
      slots.add(slot);
    }
    
    return slots;
  }
  
  /// Create a current time slot (bookable now)
  static TimeSlotInfo createCurrentTimeSlot({
    DateTime? baseTime,
    int pitchId = 1,
  }) {
    final time = baseTime ?? TestTimeScenarios.oneHourFromNow;
    
    return TimeSlotInfo(
      startTime: time,
      endTime: time.add(const Duration(hours: 1)),
      isAvailable: true,
      isBooked: false,
      pitchId: pitchId,
    );
  }
  
  /// Create a past time slot (not bookable)
  static TimeSlotInfo createPastTimeSlot({
    DateTime? baseTime,
    int pitchId = 1,
  }) {
    final time = baseTime ?? TestTimeScenarios.oneHourAgo;
    
    return TimeSlotInfo(
      startTime: time,
      endTime: time.add(const Duration(hours: 1)),
      isAvailable: false,
      isBooked: false,
      pitchId: pitchId,
    );
  }
  
  /// Create a booked time slot
  static TimeSlotInfo createBookedTimeSlot({
    DateTime? baseTime,
    int pitchId = 1,
  }) {
    final time = baseTime ?? TestTimeScenarios.oneHourFromNow;
    
    return TimeSlotInfo(
      startTime: time,
      endTime: time.add(const Duration(hours: 1)),
      isAvailable: false,
      isBooked: true,
      pitchId: pitchId,
    );
  }
}
