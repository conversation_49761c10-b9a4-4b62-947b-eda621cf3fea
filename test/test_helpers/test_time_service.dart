// test/test_helpers/test_time_service.dart

import 'package:skillz/core/services/time_service.dart';

/// Test implementation of TimeService with controllable time
/// 
/// This class extends the real TimeService to provide controlled time
/// for testing time-dependent business logic without mocking the service.
/// This ensures we test real business logic with predictable time values.
class TestTimeService extends TimeService {
  final DateTime _fixedTime;
  
  /// Create a TestTimeService that always returns the specified time
  TestTimeService(this._fixedTime);
  
  @override
  DateTime now() => _fixedTime;
  
  /// Create a TestTimeService for a specific test scenario
  factory TestTimeService.forScenario({
    required DateTime baseTime,
  }) {
    return TestTimeService(baseTime);
  }
  
  /// Create a TestTimeService for booking tests (2:30 PM on a weekday)
  factory TestTimeService.forBookingTests() {
    return TestTimeService(DateTime(2025, 7, 9, 14, 30)); // Wednesday 2:30 PM
  }
  
  /// Create a TestTimeService for cancellation tests (1 hour before booking)
  factory TestTimeService.forCancellationTests({
    required DateTime bookingTime,
  }) {
    return TestTimeService(bookingTime.subtract(const Duration(hours: 1)));
  }
  
  /// Create a TestTimeService for availability tests (morning of booking day)
  factory TestTimeService.forAvailabilityTests({
    required DateTime bookingDate,
  }) {
    return TestTimeService(DateTime(
      bookingDate.year,
      bookingDate.month,
      bookingDate.day,
      9, // 9 AM
      0,
    ));
  }
}

/// Factory for creating common test time scenarios
class TestTimeScenarios {
  /// Base time for most tests: Wednesday, July 9, 2025 at 2:30 PM
  static DateTime get baseTime => DateTime(2025, 7, 9, 14, 30);
  
  /// Tomorrow at 9 AM (for future booking tests)
  static DateTime get tomorrowAt9AM => DateTime(2025, 7, 10, 9, 0);
  
  /// Yesterday at 3 PM (for past booking tests)
  static DateTime get yesterdayAt3PM => DateTime(2025, 7, 8, 15, 0);
  
  /// Current time + 1 hour (for upcoming booking tests)
  static DateTime get oneHourFromNow => baseTime.add(const Duration(hours: 1));
  
  /// Current time - 1 hour (for past booking tests)
  static DateTime get oneHourAgo => baseTime.subtract(const Duration(hours: 1));
  
  /// Current time + 30 minutes (for current booking tests)
  static DateTime get thirtyMinutesFromNow => baseTime.add(const Duration(minutes: 30));
  
  /// Current time - 30 minutes (for recently started booking tests)
  static DateTime get thirtyMinutesAgo => baseTime.subtract(const Duration(minutes: 30));
  
  /// Create a TestTimeService for the base scenario
  static TestTimeService get baseTimeService => TestTimeService(baseTime);
  
  /// Create a TestTimeService for tomorrow morning
  static TestTimeService get tomorrowMorningTimeService => TestTimeService(tomorrowAt9AM);
  
  /// Create a TestTimeService for yesterday
  static TestTimeService get yesterdayTimeService => TestTimeService(yesterdayAt3PM);
}
