// Base test setup for widget tests
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart'; // Import SharedPreferences
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:skillz/main.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'supabase_mock_helper.dart';

/// Wraps a widget in a MaterialApp with ProviderScope for testing
Widget testableWidget({required Widget child}) {
  return ProviderScope(child: MaterialApp(home: Scaffold(body: child)));
}

/// Sets up all needed mocks for widget testing
Future<void> setupWidgetTest() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  // Mock SharedPreferences
  SharedPreferences.setMockInitialValues({});
  await setupSupabaseMocks();

  // Initialize Supabase globally with mock URL for tests
  // This prevents "You must initialize the supabase instance" errors
  try {
    await Supabase.initialize(
      url: 'https://mock.supabase.url',
      anonKey: 'mockAnonKey',
      httpClient: mockDbClient,
    );
  } catch (e) {
    // Supabase already initialized, ignore
  }
}

/// Overrides for providers in tests
List<Override> getTestProviderOverrides({bool userLoggedIn = true}) {
  return [
    // Override the supabase client provider to use our mock
    supabaseClientProvider.overrideWithValue(mockSupabaseManager.client),
    // Override auth repository to use our mock auth client
    authRepositoryProvider.overrideWithValue(
      AuthRepository(mockSupabaseManager.client),
    ),
    // Override auth state changes to use our mock auth stream
    authStateChangesProvider.overrideWith((ref) {
      if (userLoggedIn) {
        return mockAuthClient.onAuthStateChange.map(
          (authState) => authState.session?.user,
        );
      } else {
        // Return a stream that immediately emits null (unauthenticated)
        return Stream.value(null);
      }
    }),
    // Override current user provider
    currentUserProvider.overrideWith((ref) {
      return userLoggedIn ? mockAuthClient.currentUser : null;
    }),
  ];
}

/// Overrides for providers in tests (default authenticated)
final testProviderOverrides = getTestProviderOverrides(userLoggedIn: true);

/// Full app wrapped in a testable widget with necessary providers for integration tests
Widget testableApp() {
  return ProviderScope(overrides: testProviderOverrides, child: const MyApp());
}

/// Full app wrapped in a testable widget with unauthenticated user state
Widget testableAppUnauthenticated() {
  return ProviderScope(
    overrides: getTestProviderOverrides(userLoggedIn: false),
    child: const MyApp(),
  );
}
